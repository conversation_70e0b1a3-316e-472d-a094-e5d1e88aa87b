@echo off
chcp 65001 >nul
title اختبار سريع - برنامج القرعة الإلكترونية
color 0B

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           🧪 اختبار سريع للبرنامج 🧪                   ██
echo ██              Quick Test Mode                           ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

REM التحقق من Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM التحقق من المتطلبات
echo 🔍 فحص المتطلبات...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Flask غير مثبت، جاري التثبيت...
    pip install flask --quiet --user
)
echo ✅ المتطلبات متوفرة

REM اختبار سريع
echo 🧪 تشغيل اختبار سريع...
echo.

REM إضافة بيانات تجريبية
python -c "
import sqlite3
conn = sqlite3.connect('lottery.db')
cursor = conn.cursor()

# إنشاء الجداول إذا لم تكن موجودة
cursor.execute('''
    CREATE TABLE IF NOT EXISTS participants (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
''')

# إضافة بيانات تجريبية
test_names = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'عائشة أحمد', 'علي محمود']
for name in test_names:
    cursor.execute('INSERT OR IGNORE INTO participants (name) VALUES (?)', (name,))

conn.commit()
conn.close()
print('✅ تم إضافة بيانات تجريبية')
"

echo ✅ تم إعداد البيانات التجريبية
echo.
echo 🚀 تشغيل البرنامج...
echo.
echo 🌐 البرنامج سيفتح على: http://localhost:5010
echo ⏱️  انتظر 3 ثوان...

timeout /t 3 /nobreak >nul
start "" "http://localhost:5010"

echo.
echo 🎯 تشغيل الخادم...
python app.py

echo.
echo 🛑 تم إيقاف الاختبار
pause
