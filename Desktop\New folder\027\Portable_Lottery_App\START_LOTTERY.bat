@echo off
chcp 65001 >nul
title برنامج القرعة الإلكترونية - Electronic Lottery
color 0A

REM إنشاء ملف log للأخطاء
set LOG_FILE=lottery_log.txt
echo [%date% %time%] بدء تشغيل البرنامج > %LOG_FILE%

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           🎯 برنامج القرعة الإلكترونية 🎯              ██
echo ██              Electronic Lottery System                 ██
echo ██                   النسخة المحمولة                      ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 📋 جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود Python
echo [%date% %time%] فحص Python >> %LOG_FILE%
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    echo 💡 أو جرب الأمر التالي في Command Prompt:
    echo    winget install Python.Python.3
    echo.
    echo [%date% %time%] خطأ: Python غير موجود >> %LOG_FILE%
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
echo [%date% %time%] بدء تثبيت المتطلبات >> %LOG_FILE%

pip install -r requirements.txt --quiet --disable-pip-version-check 2>>%LOG_FILE%
if errorlevel 1 (
    echo ⚠️  خطأ في تثبيت المتطلبات، جاري المحاولة مع --user...
    echo [%date% %time%] محاولة تثبيت مع --user >> %LOG_FILE%
    pip install -r requirements.txt --user --quiet --disable-pip-version-check 2>>%LOG_FILE%
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo 📄 راجع ملف %LOG_FILE% للتفاصيل
        echo.
        echo 🔧 جرب تشغيل الأمر التالي يدوياً:
        echo    pip install Flask==2.3.3 --user
        echo.
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo [%date% %time%] تم تثبيت المتطلبات بنجاح >> %LOG_FILE%
echo.

REM التحقق من المنفذ 5010
echo 🔍 فحص المنفذ 5010...
netstat -an | find ":5010" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  تحذير: المنفذ 5010 قد يكون مستخدماً
    echo 💡 إذا واجهت مشاكل، أغلق البرامج الأخرى أو غير المنفذ في config.ini
    echo.
)

REM إنشاء قاعدة البيانات إذا لم تكن موجودة
if not exist "lottery.db" (
    echo 🗄️  إنشاء قاعدة البيانات الأولية...
    echo [%date% %time%] إنشاء قاعدة بيانات جديدة >> %LOG_FILE%
    echo.
)

REM تشغيل البرنامج
echo 🚀 تشغيل الخادم...
echo [%date% %time%] بدء تشغيل الخادم >> %LOG_FILE%
echo.

echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██  🌐 البرنامج متاح الآن على العناوين التالية:          ██
echo ██                                                        ██
echo ██     http://localhost:5010                              ██
echo ██     http://127.0.0.1:5010                              ██
echo ██                                                        ██
echo ██  📱 للوصول من أجهزة أخرى في نفس الشبكة:              ██
echo ██     http://[عنوان-الجهاز]:5010                         ██
echo ██                                                        ██
echo ██  📄 ملف السجل: %LOG_FILE%                              ██
echo ██  ⚠️  اضغط Ctrl+C لإيقاف البرنامج                      ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

REM فتح المتصفح تلقائياً بعد تأخير قصير
echo 🌐 فتح المتصفح تلقائياً...
timeout /t 3 /nobreak >nul
start "" "http://localhost:5010"

REM تشغيل التطبيق مع تسجيل الأخطاء
echo 🎯 تشغيل برنامج القرعة...
echo.
python app.py 2>>%LOG_FILE%

echo.
echo 🛑 تم إيقاف البرنامج
echo [%date% %time%] تم إيقاف البرنامج >> %LOG_FILE%
echo.
echo 📄 لمراجعة السجل: notepad %LOG_FILE%
echo.
pause
