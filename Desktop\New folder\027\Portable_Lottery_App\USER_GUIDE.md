# 🎯 دليل الاستخدام السريع - برنامج القرعة الإلكترونية

## 🚀 التشغيل السريع

### Windows:
1. انقر مرتين على `START_LOTTERY.bat`
2. انتظر حتى يفتح المتصفح تلقائياً
3. إذا لم يفتح، اذهب إلى: http://localhost:5010

### Linux/Mac:
1. افتح Terminal في مجلد البرنامج
2. اكتب: `./start_lottery.sh`
3. اذهب إلى: http://localhost:5010

## 📋 كيفية الاستخدام

### 1. إضافة المشاركين
- **مشارك واحد**: استخدم النموذج في الأعلى
- **عدة مشاركين**: اكتب الأسماء في المربع الكبير (اسم في كل سطر)

### 2. إجراء القرعة
- **🟡 اسحب القرعة**: قرعة عادية مع تأثيرات كاملة
- **🔴 قرعة عاجلة**: قرعة سريعة فورية
- **🔵 تدوير العجلة**: عجلة دوارة تفاعلية

### 3. إدارة المشاركين
- **حذف مشارك**: اضغط ❌ بجانب الاسم
- **مسح الكل**: زر "مسح الكل" في الأعلى

### 4. عرض التاريخ
- اضغط "تاريخ القرعات" لرؤية النتائج السابقة

## 🔧 حل المشاكل

### البرنامج لا يعمل:
1. تأكد من تثبيت Python 3.7+
2. شغل `setup.py` أولاً
3. تأكد من أن المنفذ 5010 غير مستخدم

### لا يمكن الوصول للموقع:
1. تأكد من تشغيل البرنامج
2. جرب: http://127.0.0.1:5010
3. أعد تشغيل البرنامج

### مشاكل في قاعدة البيانات:
1. احذف ملف `lottery.db`
2. أعد تشغيل البرنامج

## 📱 الوصول من أجهزة أخرى

للوصول من هاتف أو جهاز آخر في نفس الشبكة:
1. اعرف عنوان IP للجهاز المضيف
2. اذهب إلى: http://[عنوان-IP]:5010

## 🎨 الميزات

- ✅ واجهة عربية متجاوبة
- ✅ عجلة دوارة تفاعلية
- ✅ تأثيرات بصرية وصوتية
- ✅ حفظ تاريخ القرعات
- ✅ دعم الأجهزة المحمولة
- ✅ قرعة عادية وعاجلة

---
**نصيحة**: احتفظ بنسخة احتياطية من ملف `lottery.db` لحفظ بياناتك!
