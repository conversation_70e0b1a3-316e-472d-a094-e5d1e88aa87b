#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف الإعداد الأولي لبرنامج القرعة الإلكترونية
Initial Setup for Electronic Lottery Application
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def main():
    print("🔧 إعداد برنامج القرعة الإلكترونية")
    print("=" * 50)

    # التحقق من Python
    print("✅ فحص إصدار Python...")
    print(f"   Python {sys.version}")

    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        return False

    # تثبيت المتطلبات
    print("\n📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install",
            "-r", "requirements.txt", "--user", "--quiet"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError:
        print("❌ خطأ في تثبيت المتطلبات")
        return False

    # إنشاء قاعدة البيانات
    print("\n🗄️  إعداد قاعدة البيانات...")
    create_database()

    print("\n✅ تم الإعداد بنجاح!")
    print("\n🚀 لتشغيل البرنامج:")
    print("   - Windows: START_LOTTERY.bat")
    print("   - Linux/Mac: ./start_lottery.sh")

    return True

def create_database():
    """إنشاء قاعدة البيانات الأولية"""

    if Path("lottery.db").exists():
        print("   قاعدة البيانات موجودة بالفعل")
        return

    try:
        conn = sqlite3.connect('lottery.db')
        cursor = conn.cursor()

        # جدول المشاركين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS participants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول النتائج
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                winner_name TEXT NOT NULL,
                winner_email TEXT,
                winner_phone TEXT,
                total_participants INTEGER NOT NULL,
                draw_type TEXT DEFAULT 'normal',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

        print("   تم إنشاء قاعدة البيانات")

    except Exception as e:
        print(f"   خطأ في إنشاء قاعدة البيانات: {e}")

if __name__ == "__main__":
    main()
