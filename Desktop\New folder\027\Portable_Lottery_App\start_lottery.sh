#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

clear

echo -e "${CYAN}"
echo "████████████████████████████████████████████████████████████"
echo "██                                                        ██"
echo "██           🎯 برنامج القرعة الإلكترونية 🎯              ██"
echo "██              Electronic Lottery System                 ██"
echo "██                                                        ██"
echo "████████████████████████████████████████████████████████████"
echo -e "${NC}"
echo ""

echo -e "${YELLOW}📋 جاري التحقق من متطلبات النظام...${NC}"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ خطأ: Python غير مثبت على النظام${NC}"
    echo ""
    echo -e "${BLUE}📥 يرجى تثبيت Python:${NC}"
    echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "   macOS: brew install python3"
    echo ""
    exit 1
fi

# تحديد أمر Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
fi

echo -e "${GREEN}✅ Python متوفر${NC}"
echo ""

# التحقق من pip
if ! command -v $PIP_CMD &> /dev/null; then
    echo -e "${RED}❌ خطأ: pip غير متوفر${NC}"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ pip متوفر${NC}"
echo ""

# تثبيت المتطلبات
echo -e "${YELLOW}📦 تثبيت المتطلبات...${NC}"
$PIP_CMD install -r requirements.txt --quiet --disable-pip-version-check --user
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ خطأ في تثبيت المتطلبات${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم تثبيت المتطلبات بنجاح${NC}"
echo ""

# إنشاء قاعدة البيانات إذا لم تكن موجودة
if [ ! -f "lottery.db" ]; then
    echo -e "${YELLOW}🗄️  إنشاء قاعدة البيانات...${NC}"
    echo ""
fi

# تشغيل البرنامج
echo -e "${YELLOW}🚀 تشغيل الخادم...${NC}"
echo ""

echo -e "${CYAN}"
echo "████████████████████████████████████████████████████████████"
echo "██                                                        ██"
echo "██  🌐 البرنامج متاح الآن على العناوين التالية:          ██"
echo "██                                                        ██"
echo "██     http://localhost:5010                              ██"
echo "██     http://127.0.0.1:5010                              ██"
echo "██                                                        ██"
echo "██  📱 للوصول من أجهزة أخرى في نفس الشبكة:              ██"
echo "██     http://[عنوان-الجهاز]:5010                         ██"
echo "██                                                        ██"
echo "██  ⚠️  اضغط Ctrl+C لإيقاف البرنامج                      ██"
echo "██                                                        ██"
echo "████████████████████████████████████████████████████████████"
echo -e "${NC}"
echo ""

# فتح المتصفح تلقائياً (إذا كان متوفراً)
sleep 2
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5010 &> /dev/null &
elif command -v open &> /dev/null; then
    open http://localhost:5010 &> /dev/null &
fi

# تشغيل التطبيق
$PYTHON_CMD app.py

echo ""
echo -e "${RED}🛑 تم إيقاف البرنامج${NC}"
echo ""
