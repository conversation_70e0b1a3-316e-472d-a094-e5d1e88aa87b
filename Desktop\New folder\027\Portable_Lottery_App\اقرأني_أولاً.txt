🎯 برنامج القرعة الإلكترونية - النسخة المحمولة
===============================================

🚀 التشغيل السريع (في 3 خطوات):
--------------------------------

1️⃣ Windows:
   انقر مرتين على: START_LOTTERY.bat

2️⃣ Linux/Mac:
   افتح Terminal واكتب: ./start_lottery.sh

3️⃣ افتح المتصفح:
   اذهب إلى: http://localhost:5010

🧪 اختبار سريع:
---------------
انقر مرتين على: QUICK_TEST.bat
(يضيف بيانات تجريبية ويشغل البرنامج)

📋 المتطلبات الأساسية:
----------------------
✅ Python 3.7+ (مطلوب)
✅ متصفح ويب حديث
✅ اتصال إنترنت (للتثبيت الأولي فقط)

🎨 الميزات الرئيسية:
--------------------
🎡 عجلة دوارة تفاعلية كبيرة (550×550px)
🎯 قرعة عادية مع تأثيرات كاملة
⚡ قرعة عاجلة سريعة
🔊 تأثيرات صوتية متقدمة
🎨 تأثيرات بصرية وكونفيتي
📱 واجهة متجاوبة للجوال
📊 حفظ تاريخ القرعات
👥 إضافة مشاركين فردي/جماعي

🔧 حل المشاكل السريع:
---------------------

❌ "Python غير مثبت":
   حمل من: https://www.python.org/downloads/

❌ "خطأ في المتطلبات":
   شغل: pip install flask --user

❌ "المنفذ 5010 مستخدم":
   أغلق البرامج الأخرى أو غير المنفذ في config.ini

❌ "لا يمكن الوصول للموقع":
   تأكد من تشغيل البرنامج وجرب: http://127.0.0.1:5010

📱 الوصول من الجوال:
--------------------
1. اعرف عنوان IP للجهاز
2. اذهب إلى: http://[IP]:5010
مثال: http://*************:5010

📄 الملفات المهمة:
------------------
🚀 START_LOTTERY.bat - تشغيل سريع (Windows)
🚀 start_lottery.sh - تشغيل سريع (Linux/Mac)
🧪 QUICK_TEST.bat - اختبار سريع مع بيانات تجريبية
🔧 setup.py - إعداد أولي
📋 دليل_الاستخدام.md - دليل مفصل
📋 تعليمات_التثبيت.txt - تعليمات مفصلة
⚙️ config.ini - إعدادات البرنامج

🎉 استمتع بالبرنامج!
====================

💡 نصائح:
- احتفظ بنسخة احتياطية من lottery.db
- راجع ملف lottery_log.txt عند حدوث مشاكل
- يمكن تغيير المنفذ في config.ini
- البرنامج يعمل على جميع أنظمة التشغيل

📞 للدعم:
راجع الملفات المرفقة أو ملف lottery_log.txt
