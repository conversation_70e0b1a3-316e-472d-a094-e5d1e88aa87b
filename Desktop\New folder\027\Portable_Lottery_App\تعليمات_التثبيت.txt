🎯 برنامج القرعة الإلكترونية - تعليمات التثبيت والتشغيل
================================================================

📋 المتطلبات الأساسية:
-----------------------
✅ Python 3.7 أو أحدث
✅ اتصال بالإنترنت (للتثبيت الأولي فقط)
✅ متصفح ويب حديث

🚀 التشغيل السريع:
------------------

🖥️ نظام Windows:
1. انقر مرتين على ملف "START_LOTTERY.bat"
2. انتظر حتى يكتمل التثبيت والتشغيل
3. سيفتح المتصفح تلقائياً على http://localhost:5010

🐧 نظام Linux/Mac:
1. افتح Terminal في مجلد البرنامج
2. اكتب الأمر: ./start_lottery.sh
3. اذهب إلى http://localhost:5010 في المتصفح

🔧 حل المشاكل الشائعة:
-----------------------

❌ مشكلة: "Python غير مثبت"
✅ الحل: 
   - Windows: حمل Python من https://www.python.org/downloads/
   - Ubuntu: sudo apt install python3 python3-pip
   - Mac: brew install python3

❌ مشكلة: "خطأ في تثبيت المتطلبات"
✅ الحل:
   1. افتح Command Prompt/Terminal كمدير
   2. اكتب: pip install -r requirements.txt --user

❌ مشكلة: "المنفذ 5010 مستخدم"
✅ الحل:
   1. أغلق أي برامج تستخدم المنفذ 5010
   2. أو عدل ملف config.ini وغير PORT إلى رقم آخر

❌ مشكلة: "لا يمكن الوصول للموقع"
✅ الحل:
   1. تأكد من تشغيل البرنامج
   2. جرب http://127.0.0.1:5010
   3. تأكد من عدم حجب Firewall للبرنامج

📱 الوصول من أجهزة أخرى:
--------------------------
للوصول من هاتف أو جهاز آخر في نفس الشبكة:
1. اعرف عنوان IP للجهاز المضيف
2. اذهب إلى: http://[عنوان-IP]:5010

مثال: http://*************:5010

🎨 الميزات الرئيسية:
---------------------
✅ واجهة عربية متجاوبة
✅ عجلة دوارة تفاعلية كبيرة
✅ قرعة عادية مع تأثيرات كاملة
✅ قرعة عاجلة سريعة
✅ تأثيرات بصرية وصوتية
✅ حفظ تاريخ القرعات
✅ دعم الأجهزة المحمولة
✅ إضافة مشاركين فردي أو جماعي

📋 كيفية الاستخدام:
--------------------

1️⃣ إضافة المشاركين:
   - مشارك واحد: استخدم النموذج في الأعلى
   - عدة مشاركين: اكتب الأسماء في المربع (اسم في كل سطر)

2️⃣ إجراء القرعة:
   - 🟡 اسحب القرعة: قرعة عادية مع تأثيرات كاملة
   - 🔴 قرعة عاجلة: قرعة سريعة فورية
   - 🔵 تدوير العجلة: عجلة دوارة تفاعلية

3️⃣ إدارة المشاركين:
   - حذف مشارك: اضغط ❌ بجانب الاسم
   - مسح الكل: زر "مسح الكل" في الأعلى

4️⃣ عرض التاريخ:
   - اضغط "تاريخ القرعات" لرؤية النتائج السابقة

💾 النسخ الاحتياطي:
-------------------
احتفظ بنسخة احتياطية من ملف "lottery.db" لحفظ:
- قائمة المشاركين
- تاريخ القرعات
- جميع البيانات

📞 الدعم الفني:
----------------
في حالة وجود مشاكل:
1. راجع هذا الملف أولاً
2. تحقق من ملف "دليل_الاستخدام.md"
3. تأكد من تشغيل "setup.py" أولاً

🎉 استمتع بالبرنامج!
====================
