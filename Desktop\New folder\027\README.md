# برنامج القرعة الإلكترونية

برنامج ويب تفاعلي لإجراء القرعات الإلكترونية بطريقة عادلة وشفافة، مطور بلغة Python باستخدام إطار عمل Flask.

## الميزات

### 🎯 الوظائف الأساسية
- **إضافة المشاركين**: إمكانية إضافة مشارك واحد أو عدة مشاركين دفعة واحدة
- **إجراء القرعة العادية**: سحب عشوائي عادل مع تأثيرات بصرية كاملة
- **إجراء القرعة العاجلة**: سحب سريع فوري بدون انتظار طويل
- **العجلة الدوارة التفاعلية**: عجلة ملونة تعرض أسماء المشاركين وتدور لاختيار الفائز
- **عرض النتائج**: عرض الفائز مع تأثيرات بصرية وصوتية مميزة
- **تاريخ القرعات**: حفظ وعرض جميع القرعات السابقة مع تمييز القرعات العاجلة

### 🎨 واجهة المستخدم
- تصميم عربي متجاوب يدعم جميع الأجهزة
- تأثيرات بصرية وصوتية عند إعلان الفائز
- واجهة سهلة الاستخدام ومتطورة
- دعم كامل للغة العربية

### 💾 إدارة البيانات
- قاعدة بيانات SQLite لحفظ المشاركين والنتائج
- إمكانية حذف المشاركين أو مسح القائمة كاملة
- حفظ معلومات إضافية (البريد الإلكتروني، رقم الهاتف)

## متطلبات التشغيل

- Python 3.7 أو أحدث
- Flask 2.3.3
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
python app.py
```

### 3. فتح البرنامج
افتح متصفح الويب وانتقل إلى:
```
http://localhost:5010
```

## كيفية الاستخدام

### إضافة المشاركين
1. **مشارك واحد**: أدخل الاسم والمعلومات الإضافية في النموذج الأيسر
2. **عدة مشاركين**: أدخل الأسماء في المربع النصي، كل اسم في سطر منفصل

### إجراء القرعة

#### القرعة العادية:
1. تأكد من وجود مشاركين في القائمة
2. اضغط على زر "اسحب القرعة" (الأصفر)
3. انتظر النتيجة مع التأثيرات البصرية الكاملة (ثانيتان)
4. استمتع بالكونفيتي والأصوات والتأثيرات

#### القرعة العاجلة:
1. تأكد من وجود مشاركين في القائمة
2. اضغط على زر "قرعة عاجلة" (الأحمر)
3. احصل على النتيجة فوراً (نصف ثانية)
4. تأثيرات بصرية سريعة مع وميض أحمر وكونفيتي عاجل

#### العجلة الدوارة:
1. تظهر العجلة تلقائياً عند وجود مشاركين
2. كل مشارك يحصل على قسم ملون في العجلة
3. اضغط على زر "تدوير العجلة" (الأزرق)
4. شاهد العجلة تدور لمدة 3 ثوانٍ مع صوت الدوران
5. يتم تحديد الفائز بناءً على المؤشر الأحمر في الأعلى
6. تأثيرات بصرية وصوتية عند إعلان النتيجة

### إدارة المشاركين
- **حذف مشارك**: اضغط على زر الحذف بجانب اسم المشارك
- **مسح الكل**: اضغط على زر "مسح الكل" في أعلى قائمة المشاركين

### عرض التاريخ
- اضغط على "تاريخ القرعات" لعرض جميع القرعات السابقة
- يمكنك رؤية إحصائيات مفصلة عن القرعات

## هيكل المشروع

```
lottery-app/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # متطلبات Python
├── README.md             # دليل الاستخدام
├── lottery.db            # قاعدة البيانات (تُنشأ تلقائياً)
├── templates/            # قوالب HTML
│   ├── index.html        # الصفحة الرئيسية
│   └── history.html      # صفحة تاريخ القرعات
└── static/              # الملفات الثابتة
    ├── style.css        # ملف التنسيق
    └── script.js        # ملف JavaScript
```

## الميزات التقنية

### الأمان
- حماية من SQL Injection
- تشفير البيانات الحساسة
- التحقق من صحة المدخلات

### الأداء
- قاعدة بيانات محلية سريعة
- تحميل تدريجي للصفحات
- تحسين للأجهزة المحمولة

### التوافق
- يعمل على جميع أنظمة التشغيل
- متوافق مع جميع المتصفحات الحديثة
- دعم كامل للشاشات اللمسية

## التخصيص

### تغيير الألوان
عدّل ملف `static/style.css` لتغيير الألوان والتصميم

### إضافة حقول جديدة
عدّل ملف `app.py` وقوالب HTML لإضافة حقول إضافية للمشاركين

### تغيير قاعدة البيانات
يمكن تغيير SQLite إلى MySQL أو PostgreSQL بتعديل إعدادات الاتصال

## استكشاف الأخطاء

### المشاكل الشائعة
1. **خطأ في تشغيل البرنامج**: تأكد من تثبيت جميع المتطلبات
2. **لا يمكن الوصول للموقع**: تأكد من أن المنفذ 5010 غير مستخدم
3. **مشاكل في قاعدة البيانات**: احذف ملف `lottery.db` وأعد تشغيل البرنامج

### الحصول على المساعدة
- تحقق من رسائل الخطأ في Terminal
- تأكد من إصدار Python المستخدم
- راجع ملف `app.py` للتأكد من الإعدادات

## الترخيص

هذا البرنامج مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

## المطور

تم تطوير هذا البرنامج باستخدام:
- **Python**: لغة البرمجة الأساسية
- **Flask**: إطار عمل الويب
- **Bootstrap**: للتصميم المتجاوب
- **SQLite**: قاعدة البيانات
- **JavaScript**: للتفاعل والتأثيرات

---

**ملاحظة**: هذا البرنامج مصمم للاستخدام المحلي والشبكات الداخلية. للاستخدام على الإنترنت، يُنصح بإضافة طبقات أمان إضافية.
