from flask import Flask, render_template, request, jsonify, redirect, url_for
import sqlite3
import random
from datetime import datetime
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# إنشاء قاعدة البيانات
def init_db():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()

    # جدول المشاركين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS participants (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # جدول نتائج القرعات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS lottery_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            winner_name TEXT NOT NULL,
            winner_email TEXT,
            winner_phone TEXT,
            total_participants INTEGER,
            draw_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    conn.commit()
    conn.close()

# الصفحة الرئيسية
@app.route('/')
def index():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM participants ORDER BY created_at DESC')
    participants = cursor.fetchall()
    conn.close()
    return render_template('index.html', participants=participants)

# إضافة مشارك جديد
@app.route('/add_participant', methods=['POST'])
def add_participant():
    name = request.form.get('name')
    email = request.form.get('email', '')
    phone = request.form.get('phone', '')

    if name:
        conn = sqlite3.connect('lottery.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO participants (name, email, phone) VALUES (?, ?, ?)',
            (name, email, phone)
        )
        conn.commit()
        conn.close()

    return redirect(url_for('index'))

# إضافة عدة مشاركين
@app.route('/add_multiple', methods=['POST'])
def add_multiple():
    names_text = request.form.get('names_text', '')
    names = [name.strip() for name in names_text.split('\n') if name.strip()]

    if names:
        conn = sqlite3.connect('lottery.db')
        cursor = conn.cursor()
        for name in names:
            cursor.execute(
                'INSERT INTO participants (name) VALUES (?)',
                (name,)
            )
        conn.commit()
        conn.close()

    return redirect(url_for('index'))

# حذف مشارك
@app.route('/delete_participant/<int:participant_id>')
def delete_participant(participant_id):
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM participants WHERE id = ?', (participant_id,))
    conn.commit()
    conn.close()
    return redirect(url_for('index'))

# مسح جميع المشاركين
@app.route('/clear_all')
def clear_all():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM participants')
    conn.commit()
    conn.close()
    return redirect(url_for('index'))

# إجراء القرعة
@app.route('/draw_lottery')
def draw_lottery():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM participants')
    participants = cursor.fetchall()

    if not participants:
        return jsonify({'error': 'لا يوجد مشاركين في القرعة'})

    # اختيار فائز عشوائي
    winner = random.choice(participants)

    # حفظ النتيجة
    cursor.execute(
        'INSERT INTO lottery_results (winner_name, winner_email, winner_phone, total_participants) VALUES (?, ?, ?, ?)',
        (winner[1], winner[2], winner[3], len(participants))
    )
    conn.commit()
    conn.close()

    return jsonify({
        'winner': {
            'id': winner[0],
            'name': winner[1],
            'email': winner[2],
            'phone': winner[3]
        },
        'total_participants': len(participants),
        'type': 'normal'
    })

# إجراء القرعة العاجلة
@app.route('/draw_lottery_urgent')
def draw_lottery_urgent():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM participants')
    participants = cursor.fetchall()

    if not participants:
        return jsonify({'error': 'لا يوجد مشاركين في القرعة'})

    # اختيار فائز عشوائي
    winner = random.choice(participants)

    # حفظ النتيجة مع تمييز أنها قرعة عاجلة
    cursor.execute(
        'INSERT INTO lottery_results (winner_name, winner_email, winner_phone, total_participants) VALUES (?, ?, ?, ?)',
        (f"⚡ {winner[1]} (عاجل)", winner[2], winner[3], len(participants))
    )
    conn.commit()
    conn.close()

    return jsonify({
        'winner': {
            'id': winner[0],
            'name': winner[1],
            'email': winner[2],
            'phone': winner[3]
        },
        'total_participants': len(participants),
        'type': 'urgent'
    })

# عرض تاريخ القرعات
@app.route('/history')
def history():
    conn = sqlite3.connect('lottery.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM lottery_results ORDER BY draw_date DESC')
    results = cursor.fetchall()
    conn.close()
    return render_template('history.html', results=results)

if __name__ == '__main__':
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5010)
