#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف ZIP للنسخة المحمولة
Create ZIP file for portable version
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_portable_zip():
    """إنشاء ملف ZIP للنسخة المحمولة"""
    
    print("📦 إنشاء ملف ZIP للنسخة المحمولة")
    print("=" * 50)
    
    # المجلدات والملفات
    current_dir = Path(__file__).parent
    portable_dir = current_dir / "Portable_Lottery_App"
    
    # التحقق من وجود المجلد
    if not portable_dir.exists():
        print("❌ خطأ: مجلد النسخة المحمولة غير موجود")
        print("🔧 شغل portable_setup.py أولاً")
        return False
    
    # اسم ملف ZIP مع التاريخ
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = f"Electronic_Lottery_Portable_{timestamp}.zip"
    zip_path = current_dir / zip_filename
    
    print(f"📁 إنشاء ملف: {zip_filename}")
    
    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة جميع الملفات
            for root, dirs, files in os.walk(portable_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(portable_dir)
                    
                    print(f"📄 إضافة: {arc_name}")
                    zipf.write(file_path, arc_name)
        
        # حساب حجم الملف
        file_size = zip_path.stat().st_size
        size_mb = file_size / (1024 * 1024)
        
        print(f"\n✅ تم إنشاء الملف بنجاح!")
        print(f"📦 اسم الملف: {zip_filename}")
        print(f"📏 الحجم: {size_mb:.2f} MB")
        print(f"📍 المسار: {zip_path}")
        
        # إنشاء ملف معلومات
        create_info_file(current_dir, zip_filename, size_mb)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف ZIP: {e}")
        return False

def create_info_file(current_dir, zip_filename, size_mb):
    """إنشاء ملف معلومات عن النسخة المحمولة"""
    
    info_content = f"""🎯 برنامج القرعة الإلكترونية - النسخة المحمولة
Electronic Lottery System - Portable Version
=============================================

📦 معلومات الحزمة:
------------------
📄 اسم الملف: {zip_filename}
📏 الحجم: {size_mb:.2f} MB
📅 تاريخ الإنشاء: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
🔢 الإصدار: 2.0 Portable

🚀 طريقة التثبيت:
-----------------
1. استخرج ملف ZIP إلى أي مجلد
2. Windows: انقر مرتين على START_LOTTERY.bat
3. Linux/Mac: ./start_lottery.sh

📋 المتطلبات:
-------------
✅ Python 3.7 أو أحدث
✅ اتصال بالإنترنت (للتثبيت الأولي فقط)
✅ متصفح ويب حديث
✅ 50 MB مساحة فارغة

🎨 الميزات:
-----------
✅ واجهة عربية متجاوبة
✅ عجلة دوارة تفاعلية كبيرة (550×550px)
✅ قرعة عادية مع تأثيرات كاملة
✅ قرعة عاجلة سريعة
✅ تأثيرات بصرية وصوتية محسنة
✅ حفظ تاريخ القرعات
✅ دعم الأجهزة المحمولة
✅ إضافة مشاركين فردي أو جماعي
✅ مؤشر دقيق للعجلة مع تمييز الفائز
✅ أصوات متقدمة للدوران والفوز

🔧 الملفات المضمنة:
-------------------
📄 app.py - الملف الرئيسي
📄 requirements.txt - متطلبات Python
📂 static/ - ملفات CSS و JavaScript
📂 templates/ - قوالب HTML
🚀 START_LOTTERY.bat - تشغيل سريع (Windows)
🚀 start_lottery.sh - تشغيل سريع (Linux/Mac)
🔧 setup.py - إعداد أولي
📋 دليل_الاستخدام.md - دليل مفصل
📋 تعليمات_التثبيت.txt - تعليمات سريعة
⚙️ config.ini - ملف الإعدادات

🌐 الوصول للبرنامج:
-------------------
🖥️ محلي: http://localhost:5010
🌍 شبكة: http://[عنوان-IP]:5010

📞 الدعم:
---------
📄 راجع ملف "دليل_الاستخدام.md"
📄 راجع ملف "تعليمات_التثبيت.txt"
📄 راجع ملف "lottery_log.txt" عند حدوث أخطاء

🎉 استمتع بالبرنامج!
===================
"""
    
    info_file = current_dir / f"معلومات_النسخة_المحمولة.txt"
    with open(info_file, "w", encoding="utf-8") as f:
        f.write(info_content)
    
    print(f"📋 تم إنشاء ملف المعلومات: {info_file.name}")

if __name__ == "__main__":
    success = create_portable_zip()
    if success:
        print("\n🎉 النسخة المحمولة جاهزة للتوزيع!")
    else:
        print("\n❌ فشل في إنشاء النسخة المحمولة")
    
    input("\nاضغط Enter للخروج...")
