#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد النسخة المحمولة لبرنامج القرعة الإلكترونية
Portable Setup for Electronic Lottery Application
"""

import os
import sys
import shutil
import zipfile
import subprocess
from pathlib import Path

def create_portable_version():
    """إنشاء نسخة محمولة من التطبيق"""

    print("🎯 إنشاء النسخة المحمولة لبرنامج القرعة الإلكترونية")
    print("=" * 60)

    # المجلد الحالي
    current_dir = Path(__file__).parent

    # مجلد النسخة المحمولة
    portable_dir = current_dir / "Portable_Lottery_App"

    # إنشاء المجلد إذا لم يكن موجوداً
    if portable_dir.exists():
        print(f"🗑️  حذف المجلد الموجود: {portable_dir}")
        shutil.rmtree(portable_dir)

    print(f"📁 إنشاء مجلد جديد: {portable_dir}")
    portable_dir.mkdir(exist_ok=True)

    # قائمة الملفات المطلوبة
    files_to_copy = [
        "app.py",
        "requirements.txt",
        "README.md",
        "static",
        "templates"
    ]

    # نسخ الملفات
    print("\n📋 نسخ الملفات الأساسية:")
    for file_name in files_to_copy:
        source = current_dir / file_name
        dest = portable_dir / file_name

        if source.exists():
            if source.is_dir():
                print(f"📂 نسخ مجلد: {file_name}")
                shutil.copytree(source, dest)
            else:
                print(f"📄 نسخ ملف: {file_name}")
                shutil.copy2(source, dest)
        else:
            print(f"⚠️  ملف غير موجود: {file_name}")

    # إنشاء ملفات التشغيل المحسنة
    create_startup_files(portable_dir)

    # إنشاء ملف الإعداد
    create_setup_file(portable_dir)

    # إنشاء دليل الاستخدام
    create_user_guide(portable_dir)

    # إنشاء ملف الإعدادات
    create_config_file(portable_dir)

    print(f"\n✅ تم إنشاء النسخة المحمولة بنجاح في: {portable_dir}")
    print("\n🚀 لتشغيل البرنامج:")
    print("   - Windows: انقر مرتين على START_LOTTERY.bat")
    print("   - Linux/Mac: ./start_lottery.sh")

    return portable_dir

def create_startup_files(portable_dir):
    """إنشاء ملفات التشغيل المحسنة"""

    print("\n🔧 إنشاء ملفات التشغيل:")

    # ملف Windows
    windows_script = """@echo off
chcp 65001 >nul
title برنامج القرعة الإلكترونية - Electronic Lottery
color 0A

echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██           🎯 برنامج القرعة الإلكترونية 🎯              ██
echo ██              Electronic Lottery System                 ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.
echo 📋 جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo    https://www.python.org/downloads/
    echo.
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM تثبيت المتطلبات
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ❌ خطأ في تثبيت المتطلبات
    echo.
    echo 🔧 جاري المحاولة مع --user...
    pip install -r requirements.txt --user --quiet --disable-pip-version-check
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

REM إنشاء قاعدة البيانات إذا لم تكن موجودة
if not exist "lottery.db" (
    echo 🗄️  إنشاء قاعدة البيانات...
    echo.
)

REM تشغيل البرنامج
echo 🚀 تشغيل الخادم...
echo.
echo ████████████████████████████████████████████████████████████
echo ██                                                        ██
echo ██  🌐 البرنامج متاح الآن على العناوين التالية:          ██
echo ██                                                        ██
echo ██     http://localhost:5010                              ██
echo ██     http://127.0.0.1:5010                              ██
echo ██                                                        ██
echo ██  📱 للوصول من أجهزة أخرى في نفس الشبكة:              ██
echo ██     http://[عنوان-الجهاز]:5010                         ██
echo ██                                                        ██
echo ██  ⚠️  اضغط Ctrl+C لإيقاف البرنامج                      ██
echo ██                                                        ██
echo ████████████████████████████████████████████████████████████
echo.

REM فتح المتصفح تلقائياً
timeout /t 2 /nobreak >nul
start http://localhost:5010

REM تشغيل التطبيق
python app.py

echo.
echo 🛑 تم إيقاف البرنامج
echo.
pause
"""

    with open(portable_dir / "START_LOTTERY.bat", "w", encoding="utf-8") as f:
        f.write(windows_script)

    print("✅ تم إنشاء START_LOTTERY.bat")

    # ملف Linux/Mac
    linux_script = """#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
PURPLE='\\033[0;35m'
CYAN='\\033[0;36m'
NC='\\033[0m' # No Color

clear

echo -e "${CYAN}"
echo "████████████████████████████████████████████████████████████"
echo "██                                                        ██"
echo "██           🎯 برنامج القرعة الإلكترونية 🎯              ██"
echo "██              Electronic Lottery System                 ██"
echo "██                                                        ██"
echo "████████████████████████████████████████████████████████████"
echo -e "${NC}"
echo ""

echo -e "${YELLOW}📋 جاري التحقق من متطلبات النظام...${NC}"
echo ""

# التحقق من وجود Python
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ خطأ: Python غير مثبت على النظام${NC}"
    echo ""
    echo -e "${BLUE}📥 يرجى تثبيت Python:${NC}"
    echo "   Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "   CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "   macOS: brew install python3"
    echo ""
    exit 1
fi

# تحديد أمر Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
    PIP_CMD="pip3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
    PIP_CMD="pip"
fi

echo -e "${GREEN}✅ Python متوفر${NC}"
echo ""

# التحقق من pip
if ! command -v $PIP_CMD &> /dev/null; then
    echo -e "${RED}❌ خطأ: pip غير متوفر${NC}"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ pip متوفر${NC}"
echo ""

# تثبيت المتطلبات
echo -e "${YELLOW}📦 تثبيت المتطلبات...${NC}"
$PIP_CMD install -r requirements.txt --quiet --disable-pip-version-check --user
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ خطأ في تثبيت المتطلبات${NC}"
    exit 1
fi

echo -e "${GREEN}✅ تم تثبيت المتطلبات بنجاح${NC}"
echo ""

# إنشاء قاعدة البيانات إذا لم تكن موجودة
if [ ! -f "lottery.db" ]; then
    echo -e "${YELLOW}🗄️  إنشاء قاعدة البيانات...${NC}"
    echo ""
fi

# تشغيل البرنامج
echo -e "${YELLOW}🚀 تشغيل الخادم...${NC}"
echo ""

echo -e "${CYAN}"
echo "████████████████████████████████████████████████████████████"
echo "██                                                        ██"
echo "██  🌐 البرنامج متاح الآن على العناوين التالية:          ██"
echo "██                                                        ██"
echo "██     http://localhost:5010                              ██"
echo "██     http://127.0.0.1:5010                              ██"
echo "██                                                        ██"
echo "██  📱 للوصول من أجهزة أخرى في نفس الشبكة:              ██"
echo "██     http://[عنوان-الجهاز]:5010                         ██"
echo "██                                                        ██"
echo "██  ⚠️  اضغط Ctrl+C لإيقاف البرنامج                      ██"
echo "██                                                        ██"
echo "████████████████████████████████████████████████████████████"
echo -e "${NC}"
echo ""

# فتح المتصفح تلقائياً (إذا كان متوفراً)
sleep 2
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5010 &> /dev/null &
elif command -v open &> /dev/null; then
    open http://localhost:5010 &> /dev/null &
fi

# تشغيل التطبيق
$PYTHON_CMD app.py

echo ""
echo -e "${RED}🛑 تم إيقاف البرنامج${NC}"
echo ""
"""

    with open(portable_dir / "start_lottery.sh", "w", encoding="utf-8") as f:
        f.write(linux_script)

    # جعل الملف قابل للتنفيذ
    os.chmod(portable_dir / "start_lottery.sh", 0o755)

    print("✅ تم إنشاء start_lottery.sh")

def create_setup_file(portable_dir):
    """إنشاء ملف الإعداد الأولي"""

    setup_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
ملف الإعداد الأولي لبرنامج القرعة الإلكترونية
Initial Setup for Electronic Lottery Application
\"\"\"

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def main():
    print("🔧 إعداد برنامج القرعة الإلكترونية")
    print("=" * 50)

    # التحقق من Python
    print("✅ فحص إصدار Python...")
    print(f"   Python {sys.version}")

    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        return False

    # تثبيت المتطلبات
    print("\\n📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install",
            "-r", "requirements.txt", "--user", "--quiet"
        ])
        print("✅ تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError:
        print("❌ خطأ في تثبيت المتطلبات")
        return False

    # إنشاء قاعدة البيانات
    print("\\n🗄️  إعداد قاعدة البيانات...")
    create_database()

    print("\\n✅ تم الإعداد بنجاح!")
    print("\\n🚀 لتشغيل البرنامج:")
    print("   - Windows: START_LOTTERY.bat")
    print("   - Linux/Mac: ./start_lottery.sh")

    return True

def create_database():
    \"\"\"إنشاء قاعدة البيانات الأولية\"\"\"

    if Path("lottery.db").exists():
        print("   قاعدة البيانات موجودة بالفعل")
        return

    try:
        conn = sqlite3.connect('lottery.db')
        cursor = conn.cursor()

        # جدول المشاركين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS participants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول النتائج
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                winner_name TEXT NOT NULL,
                winner_email TEXT,
                winner_phone TEXT,
                total_participants INTEGER NOT NULL,
                draw_type TEXT DEFAULT 'normal',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

        print("   تم إنشاء قاعدة البيانات")

    except Exception as e:
        print(f"   خطأ في إنشاء قاعدة البيانات: {e}")

if __name__ == "__main__":
    main()
"""

    with open(portable_dir / "setup.py", "w", encoding="utf-8") as f:
        f.write(setup_script)

    print("✅ تم إنشاء setup.py")

def create_user_guide(portable_dir):
    """إنشاء دليل الاستخدام المبسط"""

    guide_content = """# 🎯 دليل الاستخدام السريع - برنامج القرعة الإلكترونية

## 🚀 التشغيل السريع

### Windows:
1. انقر مرتين على `START_LOTTERY.bat`
2. انتظر حتى يفتح المتصفح تلقائياً
3. إذا لم يفتح، اذهب إلى: http://localhost:5010

### Linux/Mac:
1. افتح Terminal في مجلد البرنامج
2. اكتب: `./start_lottery.sh`
3. اذهب إلى: http://localhost:5010

## 📋 كيفية الاستخدام

### 1. إضافة المشاركين
- **مشارك واحد**: استخدم النموذج في الأعلى
- **عدة مشاركين**: اكتب الأسماء في المربع الكبير (اسم في كل سطر)

### 2. إجراء القرعة
- **🟡 اسحب القرعة**: قرعة عادية مع تأثيرات كاملة
- **🔴 قرعة عاجلة**: قرعة سريعة فورية
- **🔵 تدوير العجلة**: عجلة دوارة تفاعلية

### 3. إدارة المشاركين
- **حذف مشارك**: اضغط ❌ بجانب الاسم
- **مسح الكل**: زر "مسح الكل" في الأعلى

### 4. عرض التاريخ
- اضغط "تاريخ القرعات" لرؤية النتائج السابقة

## 🔧 حل المشاكل

### البرنامج لا يعمل:
1. تأكد من تثبيت Python 3.7+
2. شغل `setup.py` أولاً
3. تأكد من أن المنفذ 5010 غير مستخدم

### لا يمكن الوصول للموقع:
1. تأكد من تشغيل البرنامج
2. جرب: http://127.0.0.1:5010
3. أعد تشغيل البرنامج

### مشاكل في قاعدة البيانات:
1. احذف ملف `lottery.db`
2. أعد تشغيل البرنامج

## 📱 الوصول من أجهزة أخرى

للوصول من هاتف أو جهاز آخر في نفس الشبكة:
1. اعرف عنوان IP للجهاز المضيف
2. اذهب إلى: http://[عنوان-IP]:5010

## 🎨 الميزات

- ✅ واجهة عربية متجاوبة
- ✅ عجلة دوارة تفاعلية
- ✅ تأثيرات بصرية وصوتية
- ✅ حفظ تاريخ القرعات
- ✅ دعم الأجهزة المحمولة
- ✅ قرعة عادية وعاجلة

---
**نصيحة**: احتفظ بنسخة احتياطية من ملف `lottery.db` لحفظ بياناتك!
"""

    with open(portable_dir / "دليل_الاستخدام.md", "w", encoding="utf-8") as f:
        f.write(guide_content)

    with open(portable_dir / "USER_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide_content)

    print("✅ تم إنشاء دليل الاستخدام")

def create_config_file(portable_dir):
    """إنشاء ملف الإعدادات"""

    config_content = """# إعدادات برنامج القرعة الإلكترونية
# Electronic Lottery Configuration

[SERVER]
# منفذ الخادم (Port)
PORT = 5010

# عنوان الخادم (Host)
HOST = 0.0.0.0

# وضع التطوير (Debug Mode)
DEBUG = False

[DATABASE]
# اسم ملف قاعدة البيانات
DB_NAME = lottery.db

[SECURITY]
# مفتاح التشفير (يتم إنشاؤه تلقائياً)
SECRET_KEY = auto-generated

[FEATURES]
# تفعيل العجلة الدوارة
ENABLE_WHEEL = True

# تفعيل الأصوات
ENABLE_SOUNDS = True

# تفعيل التأثيرات البصرية
ENABLE_EFFECTS = True

# الحد الأقصى للمشاركين
MAX_PARTICIPANTS = 1000

[UI]
# لغة الواجهة
LANGUAGE = ar

# اتجاه النص
TEXT_DIRECTION = rtl

# السمة (Theme)
THEME = default
"""

    with open(portable_dir / "config.ini", "w", encoding="utf-8") as f:
        f.write(config_content)

    print("✅ تم إنشاء ملف الإعدادات")

if __name__ == "__main__":
    create_portable_version()
