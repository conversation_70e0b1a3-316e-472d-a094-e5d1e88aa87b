// وظائف القرعة الإلكترونية

// إجراء القرعة العادية
function drawLottery() {
    const drawBtn = document.getElementById('draw-btn');
    const lotterySection = document.getElementById('lottery-section');
    const resultSection = document.getElementById('result-section');
    const loading = document.getElementById('loading');

    // تشغيل أصوات بداية القرعة
    playLotteryStartSound();

    // تأثيرات بصرية للبداية
    addLotteryStartEffect();

    // إخفاء الزر وإظهار التحميل
    lotterySection.style.display = 'none';
    loading.style.display = 'block';

    // محاكاة وقت السحب للتشويق
    setTimeout(() => {
        // إجراء طلب AJAX للسحب
        fetch('/draw_lottery')
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';

                if (data.error) {
                    alert(data.error);
                    lotterySection.style.display = 'block';
                    return;
                }

                // عرض النتيجة
                displayWinner(data);
                resultSection.style.display = 'block';

                // تشغيل صوت النجاح المحسن
                playSuccessSound();

                // إضافة تأثيرات بصرية
                addConfetti();
            })
            .catch(error => {
                console.error('خطأ في السحب:', error);
                loading.style.display = 'none';
                lotterySection.style.display = 'block';
                alert('حدث خطأ أثناء السحب. يرجى المحاولة مرة أخرى.');
            });
    }, 2000); // انتظار ثانيتين للتشويق
}

// إجراء القرعة العاجلة
function drawLotteryUrgent() {
    const lotterySection = document.getElementById('lottery-section');
    const resultSection = document.getElementById('result-section');
    const loading = document.getElementById('loading');

    // تشغيل أصوات بداية القرعة العاجلة
    playUrgentStartSound();

    // تأثيرات بصرية عاجلة للبداية
    addUrgentStartEffect();

    // إخفاء الأزرار وإظهار التحميل السريع
    lotterySection.style.display = 'none';
    loading.style.display = 'block';

    // تحديث نص التحميل للقرعة العاجلة
    const loadingText = loading.querySelector('p');
    if (loadingText) {
        loadingText.textContent = 'قرعة عاجلة جاري السحب...';
    }

    // سحب فوري بدون انتظار طويل
    setTimeout(() => {
        fetch('/draw_lottery_urgent')
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';

                if (data.error) {
                    alert(data.error);
                    lotterySection.style.display = 'block';
                    return;
                }

                // عرض النتيجة مع تمييز القرعة العاجلة
                displayWinner(data, true);
                resultSection.style.display = 'block';

                // تشغيل صوت النجاح العاجل المحسن
                playUrgentSound();

                // تأثيرات بصرية سريعة
                addUrgentEffect();
            })
            .catch(error => {
                console.error('خطأ في القرعة العاجلة:', error);
                loading.style.display = 'none';
                lotterySection.style.display = 'block';
                alert('حدث خطأ أثناء القرعة العاجلة. يرجى المحاولة مرة أخرى.');
            });
    }, 500); // انتظار نصف ثانية فقط للقرعة العاجلة
}

// عرض الفائز
function displayWinner(data, isUrgent = false) {
    const winnerName = document.getElementById('winner-name');
    const winnerDetails = document.getElementById('winner-details');
    const resultSection = document.getElementById('result-section');

    // تمييز القرعة العاجلة
    if (isUrgent) {
        winnerName.innerHTML = `⚡ ${data.winner.name} ⚡`;
        winnerName.style.color = '#dc3545';
        winnerName.style.textShadow = '2px 2px 4px rgba(220, 53, 69, 0.3)';
        resultSection.querySelector('.alert').className = 'alert alert-danger text-center';
        const headerElement = resultSection.querySelector('h5') || resultSection.querySelector('h4');
        if (headerElement) {
            headerElement.innerHTML = '<i class="fas fa-bolt"></i> الفائز في القرعة العاجلة:';
        }
    } else {
        winnerName.textContent = data.winner.name;
        winnerName.style.color = '#007bff';
        winnerName.style.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.1)';
        resultSection.querySelector('.alert').className = 'alert alert-success text-center';
        const headerElement = resultSection.querySelector('h5') || resultSection.querySelector('h4');
        if (headerElement) {
            headerElement.innerHTML = '<i class="fas fa-trophy"></i> الفائز هو:';
        }
    }

    let details = `من بين ${data.total_participants} مشارك`;
    if (isUrgent) {
        details = `🔥 قرعة عاجلة - ${details}`;
    }

    if (data.winner.email) {
        details += `<br><i class="fas fa-envelope"></i> ${data.winner.email}`;
    }
    if (data.winner.phone) {
        details += `<br><i class="fas fa-phone"></i> ${data.winner.phone}`;
    }

    winnerDetails.innerHTML = details;
}

// إعادة تعيين القرعة
function resetLottery() {
    const lotterySection = document.getElementById('lottery-section');
    const resultSection = document.getElementById('result-section');
    const loadingText = document.getElementById('loading').querySelector('p');

    resultSection.style.display = 'none';
    lotterySection.style.display = 'block';

    // إعادة تعيين نص التحميل
    if (loadingText) {
        loadingText.textContent = 'جاري سحب القرعة...';
    }

    // تحديث العجلة
    initWheel();
}

// تحديث العجلة عند تغيير المشاركين
function updateWheel() {
    setTimeout(() => {
        initWheel();
    }, 100);
}

// معالجة إرسال النماذج
function handleFormSubmit(form) {
    console.log('تم إرسال نموذج إضافة مشارك');

    // إضافة مؤشر تحميل
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';

        // إعادة تفعيل الزر بعد فترة
        setTimeout(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }, 2000);
    }

    // تحديث العجلة بعد إرسال النموذج
    setTimeout(() => {
        console.log('تحديث العجلة بعد إضافة مشارك');
        initWheel();

        // تحديث إضافي للتأكد
        setTimeout(() => {
            initWheel();
        }, 1000);
    }, 500);

    return true; // السماح بإرسال النموذج
}

// تشغيل صوت النجاح المحسن للقرعة العادية
function playSuccessSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // لحن النجاح الأساسي
        playVictoryFanfare(audioContext);

        // أصوات الطبول
        setTimeout(() => playDrumRoll(audioContext), 200);

        // أصوات الأجراس
        setTimeout(() => playChimes(audioContext), 800);

        // صوت التصفيق
        setTimeout(() => playApplause(audioContext), 1200);

    } catch (error) {
        console.log('لا يمكن تشغيل الصوت:', error);
    }
}

// لحن النجاح الكامل
function playVictoryFanfare(audioContext) {
    const notes = [
        { freq: 523.25, time: 0, duration: 0.3 },    // C5
        { freq: 659.25, time: 0.2, duration: 0.3 },  // E5
        { freq: 783.99, time: 0.4, duration: 0.3 },  // G5
        { freq: 1046.50, time: 0.6, duration: 0.5 }, // C6
        { freq: 783.99, time: 1.0, duration: 0.2 },  // G5
        { freq: 1046.50, time: 1.2, duration: 0.8 }  // C6 طويل
    ];

    notes.forEach(note => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter = audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(note.freq, audioContext.currentTime);

            // فلتر للحصول على صوت أكثر ثراءً
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(2000, audioContext.currentTime);
            filter.Q.setValueAtTime(1, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.25, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + note.duration);

            oscillator.type = 'triangle';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + note.duration);
        }, note.time * 1000);
    });
}

// صوت الطبول
function playDrumRoll(audioContext) {
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            const noiseBuffer = createNoiseBuffer(audioContext, 0.1);
            const noiseSource = audioContext.createBufferSource();
            const filter = audioContext.createBiquadFilter();
            const gainNode = audioContext.createGain();

            noiseSource.buffer = noiseBuffer;
            noiseSource.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // فلتر للحصول على صوت طبل
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(200, audioContext.currentTime);
            filter.Q.setValueAtTime(10, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            noiseSource.start(audioContext.currentTime);
            noiseSource.stop(audioContext.currentTime + 0.1);
        }, i * 80);
    }
}

// أصوات الأجراس
function playChimes(audioContext) {
    const chimeFreqs = [1047, 1319, 1568, 2093]; // C6, E6, G6, C7

    chimeFreqs.forEach((freq, index) => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter = audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);

            // فلتر للحصول على صوت جرس
            filter.type = 'peaking';
            filter.frequency.setValueAtTime(freq, audioContext.currentTime);
            filter.Q.setValueAtTime(30, audioContext.currentTime);
            filter.gain.setValueAtTime(20, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 2);

            oscillator.type = 'sine';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 2);
        }, index * 150);
    });
}

// صوت التصفيق
function playApplause(audioContext) {
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const noiseBuffer = createNoiseBuffer(audioContext, 0.05);
            const noiseSource = audioContext.createBufferSource();
            const filter = audioContext.createBiquadFilter();
            const gainNode = audioContext.createGain();

            noiseSource.buffer = noiseBuffer;
            noiseSource.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // فلتر للحصول على صوت تصفيق
            filter.type = 'bandpass';
            filter.frequency.setValueAtTime(1000 + Math.random() * 2000, audioContext.currentTime);
            filter.Q.setValueAtTime(0.5, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.08, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);

            noiseSource.start(audioContext.currentTime);
            noiseSource.stop(audioContext.currentTime + 0.05);
        }, Math.random() * 1000);
    }
}

// أصوات بداية القرعة العادية
function playLotteryStartSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت بداية مثير
        playLotteryIntro(audioContext);

        // أصوات الترقب
        setTimeout(() => playAnticipationSound(audioContext), 200);

        // صوت الطبول التحضيرية
        setTimeout(() => playPreparationDrums(audioContext), 400);

    } catch (error) {
        console.log('لا يمكن تشغيل أصوات القرعة:', error);
    }
}

// صوت مقدمة القرعة
function playLotteryIntro(audioContext) {
    const introNotes = [
        { freq: 261.63, time: 0, duration: 0.3 },    // C4
        { freq: 329.63, time: 0.15, duration: 0.3 }, // E4
        { freq: 392.00, time: 0.3, duration: 0.3 },  // G4
        { freq: 523.25, time: 0.45, duration: 0.5 }  // C5
    ];

    introNotes.forEach(note => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter = audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(note.freq, audioContext.currentTime);

            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(1500, audioContext.currentTime);
            filter.Q.setValueAtTime(1, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + note.duration);

            oscillator.type = 'triangle';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + note.duration);
        }, note.time * 1000);
    });
}

// أصوات الترقب
function playAnticipationSound(audioContext) {
    for (let i = 0; i < 6; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(440 + (i * 50), audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

            oscillator.type = 'sine';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.15);
        }, i * 100);
    }
}

// طبول تحضيرية
function playPreparationDrums(audioContext) {
    for (let i = 0; i < 6; i++) {
        setTimeout(() => {
            const noiseBuffer = createNoiseBuffer(audioContext, 0.08);
            const noiseSource = audioContext.createBufferSource();
            const filter = audioContext.createBiquadFilter();
            const gainNode = audioContext.createGain();

            noiseSource.buffer = noiseBuffer;
            noiseSource.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(300, audioContext.currentTime);
            filter.Q.setValueAtTime(8, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.12, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.08);

            noiseSource.start(audioContext.currentTime);
            noiseSource.stop(audioContext.currentTime + 0.08);
        }, i * 120);
    }
}

// تأثيرات بصرية لبداية القرعة
function addLotteryStartEffect() {
    // إضافة توهج ذهبي للزر
    const drawBtn = document.getElementById('draw-btn');
    if (drawBtn) {
        drawBtn.style.boxShadow = '0 0 20px rgba(255, 193, 7, 0.8)';
        drawBtn.style.transform = 'scale(1.05)';

        setTimeout(() => {
            drawBtn.style.boxShadow = '';
            drawBtn.style.transform = 'scale(1)';
        }, 800);
    }

    // كونفيتي ذهبي خفيف
    for (let i = 0; i < 15; i++) {
        createLotteryConfettiPiece();
    }
}

// كونفيتي خاص بالقرعة العادية
function createLotteryConfettiPiece() {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.width = '8px';
    confetti.style.height = '8px';
    confetti.style.backgroundColor = getLotteryColor();
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.pointerEvents = 'none';

    document.body.appendChild(confetti);

    const animation = confetti.animate([
        { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(100vh) rotate(${Math.random() * 360}deg)`, opacity: 0 }
    ], {
        duration: Math.random() * 2000 + 1500,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    });

    animation.onfinish = () => {
        confetti.remove();
    };
}

// ألوان القرعة العادية
function getLotteryColor() {
    const colors = ['#ffc107', '#fd7e14', '#20c997', '#6f42c1', '#e83e8c'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// أصوات بداية القرعة العاجلة
function playUrgentStartSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت إنذار فوري
        playUrgentStartAlarm(audioContext);

        // أصوات التحذير
        setTimeout(() => playWarningBeeps(audioContext), 50);

        // صوت الاستعداد السريع
        setTimeout(() => playQuickPreparation(audioContext), 200);

    } catch (error) {
        console.log('لا يمكن تشغيل أصوات القرعة العاجلة:', error);
    }
}

// صوت إنذار بداية عاجل
function playUrgentStartAlarm(audioContext) {
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1500, audioContext.currentTime + 0.05);
            oscillator.frequency.setValueAtTime(1200, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.25, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

            oscillator.type = 'square';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.15);
        }, i * 150);
    }
}

// أصوات التحذير السريعة
function playWarningBeeps(audioContext) {
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(1800, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.06);

            oscillator.type = 'sine';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.06);
        }, i * 80);
    }
}

// صوت الاستعداد السريع
function playQuickPreparation(audioContext) {
    const quickNotes = [
        { freq: 523.25, time: 0, duration: 0.15 },    // C5
        { freq: 659.25, time: 0.1, duration: 0.15 },  // E5
        { freq: 783.99, time: 0.2, duration: 0.2 }    // G5
    ];

    quickNotes.forEach(note => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(note.freq, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.18, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + note.duration);

            oscillator.type = 'triangle';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + note.duration);
        }, note.time * 1000);
    });
}

// تأثيرات بصرية لبداية القرعة العاجلة
function addUrgentStartEffect() {
    // إضافة وميض أحمر للزر
    const urgentBtn = document.getElementById('draw-urgent-btn');
    if (urgentBtn) {
        urgentBtn.style.boxShadow = '0 0 25px rgba(220, 53, 69, 0.9)';
        urgentBtn.style.transform = 'scale(1.1)';

        // وميض سريع
        let flashCount = 0;
        const flashInterval = setInterval(() => {
            urgentBtn.style.backgroundColor = flashCount % 2 === 0 ? '#dc3545' : '#ff1744';
            flashCount++;
            if (flashCount >= 6) {
                clearInterval(flashInterval);
                urgentBtn.style.backgroundColor = '';
                urgentBtn.style.boxShadow = '';
                urgentBtn.style.transform = 'scale(1)';
            }
        }, 100);
    }

    // كونفيتي أحمر سريع
    for (let i = 0; i < 20; i++) {
        createUrgentStartConfettiPiece();
    }
}

// كونفيتي خاص ببداية القرعة العاجلة
function createUrgentStartConfettiPiece() {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.width = '6px';
    confetti.style.height = '6px';
    confetti.style.backgroundColor = getUrgentStartColor();
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.pointerEvents = 'none';

    document.body.appendChild(confetti);

    const animation = confetti.animate([
        { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(100vh) rotate(${Math.random() * 720}deg)`, opacity: 0 }
    ], {
        duration: Math.random() * 1000 + 800,
        easing: 'cubic-bezier(0.7, 0, 0.3, 1)'
    });

    animation.onfinish = () => {
        confetti.remove();
    };
}

// ألوان بداية القرعة العاجلة
function getUrgentStartColor() {
    const colors = ['#dc3545', '#ff6b6b', '#e74c3c', '#ff1744', '#d32f2f'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// إضافة تأثيرات الكونفيتي
function addConfetti() {
    // إنشاء عناصر الكونفيتي
    for (let i = 0; i < 50; i++) {
        createConfettiPiece();
    }
}

function createConfettiPiece() {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.width = '10px';
    confetti.style.height = '10px';
    confetti.style.backgroundColor = getRandomColor();
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.pointerEvents = 'none';

    document.body.appendChild(confetti);

    // تحريك الكونفيتي
    const animation = confetti.animate([
        { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(100vh) rotate(${Math.random() * 360}deg)`, opacity: 0 }
    ], {
        duration: Math.random() * 3000 + 2000,
        easing: 'cubic-bezier(0.5, 0, 0.5, 1)'
    });

    animation.onfinish = () => {
        confetti.remove();
    };
}

function getRandomColor() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة العجلة الدوارة
    initWheel();

    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحسين النماذج وإضافة تحديث العجلة
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';

                // تحديث العجلة بعد إرسال النموذج
                setTimeout(() => {
                    console.log('تحديث العجلة بعد إرسال النموذج');
                    initWheel();
                }, 500);

                // تحديث إضافي بعد تحميل الصفحة
                setTimeout(() => {
                    console.log('تحديث إضافي للعجلة');
                    initWheel();
                }, 1500);
            }
        });
    });

    // إضافة تأثيرات للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // تحسين قائمة المشاركين
    const participantItems = document.querySelectorAll('.participant-item');
    participantItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#e3f2fd';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});

// وظائف إضافية للتحسين
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;

    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    return isValid;
}

// إضافة تأثيرات صوتية للأزرار
function addButtonSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
        // تجاهل الأخطاء الصوتية
    }
}

// تحسين الاستجابة للشاشات الصغيرة
function adjustForMobile() {
    if (window.innerWidth < 768) {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.marginBottom = '15px';
        });
    }
}

window.addEventListener('resize', adjustForMobile);
window.addEventListener('load', adjustForMobile);

// تحديث العجلة عند تحميل الصفحة
window.addEventListener('load', function() {
    setTimeout(initWheel, 500);

    // إضافة أصوات تفاعلية لزر العجلة
    const spinBtn = document.getElementById('spin-wheel-btn');
    if (spinBtn) {
        spinBtn.addEventListener('mouseenter', () => {
            if (!isSpinning) {
                playButtonHoverSound();
            }
        });

        spinBtn.addEventListener('click', () => {
            if (!isSpinning) {
                playButtonClickSound();
            }
        });
    }

    // مراقبة التغييرات في قائمة المشاركين
    const participantsContainer = document.querySelector('.participants-horizontal');
    if (participantsContainer) {
        console.log('تم العثور على حاوية المشاركين، بدء المراقبة');

        const observer = new MutationObserver(function(mutations) {
            console.log('تم اكتشاف تغيير في قائمة المشاركين');
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    console.log('تغيير في العناصر الفرعية، تحديث العجلة');
                    setTimeout(() => {
                        initWheel();
                    }, 100);
                }
            });
        });

        observer.observe(participantsContainer, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true
        });
    } else {
        console.log('لم يتم العثور على حاوية المشاركين');
    }

    // مراقبة إضافية للصفحة كاملة
    const pageObserver = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        mutations.forEach(function(mutation) {
            if (mutation.target.classList &&
                (mutation.target.classList.contains('participants-horizontal') ||
                 mutation.target.closest('.participants-horizontal'))) {
                shouldUpdate = true;
            }
        });

        if (shouldUpdate) {
            console.log('تحديث العجلة من المراقب العام');
            setTimeout(initWheel, 200);
        }
    });

    pageObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// صوت التمرير على زر العجلة
function playButtonHoverSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.05, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.type = 'sine';
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
        // تجاهل الأخطاء
    }
}

// صوت النقر على زر العجلة
function playButtonClickSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت نقرة قوية
        const oscillator1 = audioContext.createOscillator();
        const gainNode1 = audioContext.createGain();

        oscillator1.connect(gainNode1);
        gainNode1.connect(audioContext.destination);

        oscillator1.frequency.setValueAtTime(400, audioContext.currentTime);
        gainNode1.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode1.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator1.type = 'square';
        oscillator1.start(audioContext.currentTime);
        oscillator1.stop(audioContext.currentTime + 0.1);

        // صوت إضافي للتأكيد
        setTimeout(() => {
            const oscillator2 = audioContext.createOscillator();
            const gainNode2 = audioContext.createGain();

            oscillator2.connect(gainNode2);
            gainNode2.connect(audioContext.destination);

            oscillator2.frequency.setValueAtTime(600, audioContext.currentTime);
            gainNode2.gain.setValueAtTime(0.08, audioContext.currentTime);
            gainNode2.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.08);

            oscillator2.type = 'triangle';
            oscillator2.start(audioContext.currentTime);
            oscillator2.stop(audioContext.currentTime + 0.08);
        }, 50);

    } catch (error) {
        // تجاهل الأخطاء
    }
}

// تشغيل صوت القرعة العاجلة المحسن
function playUrgentSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت إنذار عاجل
        playUrgentAlarm(audioContext);

        // أصوات الطوارئ
        setTimeout(() => playEmergencyBeeps(audioContext), 100);

        // صوت السيرين
        setTimeout(() => playSiren(audioContext), 300);

        // أصوات الطبول السريعة
        setTimeout(() => playUrgentDrums(audioContext), 600);

        // لحن النجاح العاجل
        setTimeout(() => playUrgentVictory(audioContext), 1000);

    } catch (error) {
        console.log('لا يمكن تشغيل الصوت:', error);
    }
}

// صوت الإنذار العاجل
function playUrgentAlarm(audioContext) {
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter = audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // تردد عالي ومتغير للإنذار
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1500, audioContext.currentTime + 0.05);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

            filter.type = 'bandpass';
            filter.frequency.setValueAtTime(1200, audioContext.currentTime);
            filter.Q.setValueAtTime(5, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.15);

            oscillator.type = 'square';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.15);
        }, i * 200);
    }
}

// أصوات الطوارئ المتتالية
function playEmergencyBeeps(audioContext) {
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(2000, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.25, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.08);

            oscillator.type = 'sine';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.08);
        }, i * 100);
    }
}

// صوت السيرين
function playSiren(audioContext) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();

    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // تردد متذبذب للسيرين
    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
    oscillator.frequency.linearRampToValueAtTime(800, audioContext.currentTime + 0.5);
    oscillator.frequency.linearRampToValueAtTime(400, audioContext.currentTime + 1.0);

    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(1000, audioContext.currentTime);

    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1.0);

    oscillator.type = 'sawtooth';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 1.0);
}

// طبول عاجلة سريعة
function playUrgentDrums(audioContext) {
    for (let i = 0; i < 12; i++) {
        setTimeout(() => {
            const noiseBuffer = createNoiseBuffer(audioContext, 0.06);
            const noiseSource = audioContext.createBufferSource();
            const filter = audioContext.createBiquadFilter();
            const gainNode = audioContext.createGain();

            noiseSource.buffer = noiseBuffer;
            noiseSource.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // فلتر للحصول على صوت طبل قوي
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(150, audioContext.currentTime);
            filter.Q.setValueAtTime(15, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.06);

            noiseSource.start(audioContext.currentTime);
            noiseSource.stop(audioContext.currentTime + 0.06);
        }, i * 50);
    }
}

// لحن النجاح العاجل
function playUrgentVictory(audioContext) {
    const urgentNotes = [
        { freq: 1047, time: 0, duration: 0.2 },    // C6
        { freq: 1319, time: 0.1, duration: 0.2 },  // E6
        { freq: 1568, time: 0.2, duration: 0.2 },  // G6
        { freq: 2093, time: 0.3, duration: 0.4 },  // C7
        { freq: 1568, time: 0.6, duration: 0.15 }, // G6
        { freq: 2093, time: 0.75, duration: 0.6 }  // C7 طويل
    ];

    urgentNotes.forEach(note => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            const filter = audioContext.createBiquadFilter();

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(note.freq, audioContext.currentTime);

            // فلتر للحصول على صوت حاد ومشرق
            filter.type = 'highpass';
            filter.frequency.setValueAtTime(500, audioContext.currentTime);
            filter.Q.setValueAtTime(2, audioContext.currentTime);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + note.duration);

            oscillator.type = 'square';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + note.duration);
        }, note.time * 1000);
    });
}

// تأثيرات بصرية للقرعة العاجلة
function addUrgentEffect() {
    // إضافة وميض أحمر للشاشة
    const flashOverlay = document.createElement('div');
    flashOverlay.style.position = 'fixed';
    flashOverlay.style.top = '0';
    flashOverlay.style.left = '0';
    flashOverlay.style.width = '100vw';
    flashOverlay.style.height = '100vh';
    flashOverlay.style.backgroundColor = 'rgba(220, 53, 69, 0.3)';
    flashOverlay.style.zIndex = '9998';
    flashOverlay.style.pointerEvents = 'none';

    document.body.appendChild(flashOverlay);

    // تأثير الوميض
    const animation = flashOverlay.animate([
        { opacity: 0 },
        { opacity: 1 },
        { opacity: 0 },
        { opacity: 1 },
        { opacity: 0 }
    ], {
        duration: 1000,
        easing: 'ease-in-out'
    });

    animation.onfinish = () => {
        flashOverlay.remove();
    };

    // إضافة كونفيتي أحمر سريع
    for (let i = 0; i < 30; i++) {
        createUrgentConfettiPiece();
    }
}

function createUrgentConfettiPiece() {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.width = '8px';
    confetti.style.height = '8px';
    confetti.style.backgroundColor = getUrgentColor();
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.pointerEvents = 'none';

    document.body.appendChild(confetti);

    // تحريك سريع للكونفيتي
    const animation = confetti.animate([
        { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(100vh) rotate(${Math.random() * 720}deg)`, opacity: 0 }
    ], {
        duration: Math.random() * 1500 + 1000,
        easing: 'cubic-bezier(0.7, 0, 0.3, 1)'
    });

    animation.onfinish = () => {
        confetti.remove();
    };
}

function getUrgentColor() {
    const colors = ['#dc3545', '#ff6b6b', '#e74c3c', '#c0392b', '#a93226', '#922b21'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// متغيرات العجلة الدوارة
let wheelCanvas;
let wheelCtx;
let participants = [];
let wheelColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F39C12'];
let isSpinning = false;
let currentRotation = 0;

// تهيئة العجلة الدوارة
function initWheel() {
    wheelCanvas = document.getElementById('wheel-canvas');
    if (!wheelCanvas) {
        console.log('لم يتم العثور على canvas العجلة');
        return;
    }

    wheelCtx = wheelCanvas.getContext('2d');

    // الحصول على المشاركين من الصفحة (من الشارات الجديدة)
    const participantElements = document.querySelectorAll('.participants-horizontal .badge small');
    participants = Array.from(participantElements).map(el => el.textContent.trim());

    console.log('المشاركين المكتشفين من الشارات:', participants);

    // إذا لم توجد شارات، جرب الطريقة القديمة
    if (participants.length === 0) {
        const oldElements = document.querySelectorAll('.participant-item strong');
        participants = Array.from(oldElements).map(el => el.textContent.trim());
        console.log('المشاركين من الطريقة القديمة:', participants);
    }

    // جرب طريقة أخرى: البحث في جميع الشارات
    if (participants.length === 0) {
        const allBadges = document.querySelectorAll('.participants-horizontal .badge');
        participants = Array.from(allBadges).map(badge => {
            const smallElement = badge.querySelector('small');
            return smallElement ? smallElement.textContent.trim() : badge.textContent.trim().replace('×', '').trim();
        }).filter(name => name && name.length > 0);
        console.log('المشاركين من جميع الشارات:', participants);
    }

    console.log('العدد النهائي للمشاركين:', participants.length);

    if (participants.length > 0) {
        drawWheel();
        const wheelContainer = document.getElementById('wheel-container');
        if (wheelContainer) {
            wheelContainer.style.display = 'block';
        }
    } else {
        const wheelContainer = document.getElementById('wheel-container');
        if (wheelContainer) {
            wheelContainer.style.display = 'none';
        }
    }
}

// رسم العجلة
function drawWheel() {
    if (!wheelCtx || participants.length === 0) return;

    const centerX = wheelCanvas.width / 2;
    const centerY = wheelCanvas.height / 2;
    const radius = 250;
    const anglePerSegment = (2 * Math.PI) / participants.length;

    // مسح الكانفاس
    wheelCtx.clearRect(0, 0, wheelCanvas.width, wheelCanvas.height);

    // رسم الخلفية
    wheelCtx.beginPath();
    wheelCtx.arc(centerX, centerY, radius + 5, 0, 2 * Math.PI);
    wheelCtx.fillStyle = '#2c3e50';
    wheelCtx.fill();

    // رسم الأقسام
    for (let i = 0; i < participants.length; i++) {
        const startAngle = i * anglePerSegment + currentRotation;
        const endAngle = (i + 1) * anglePerSegment + currentRotation;

        // رسم القسم
        wheelCtx.beginPath();
        wheelCtx.moveTo(centerX, centerY);
        wheelCtx.arc(centerX, centerY, radius, startAngle, endAngle);
        wheelCtx.closePath();
        wheelCtx.fillStyle = wheelColors[i % wheelColors.length];
        wheelCtx.fill();

        // رسم الحدود
        wheelCtx.strokeStyle = '#fff';
        wheelCtx.lineWidth = 3;
        wheelCtx.stroke();

        // رسم النص
        const textAngle = startAngle + anglePerSegment / 2;
        const textX = centerX + Math.cos(textAngle) * (radius * 0.7);
        const textY = centerY + Math.sin(textAngle) * (radius * 0.7);

        wheelCtx.save();
        wheelCtx.translate(textX, textY);
        wheelCtx.rotate(textAngle + Math.PI / 2);
        wheelCtx.fillStyle = '#fff';
        wheelCtx.font = 'bold 22px Arial';
        wheelCtx.textAlign = 'center';
        wheelCtx.textBaseline = 'middle';

        // تقصير النص إذا كان طويلاً
        let displayName = participants[i];
        if (displayName.length > 12) {
            displayName = displayName.substring(0, 12) + '...';
        }

        wheelCtx.fillText(displayName, 0, 0);
        wheelCtx.restore();
    }

    // رسم المركز
    wheelCtx.beginPath();
    wheelCtx.arc(centerX, centerY, 35, 0, 2 * Math.PI);
    wheelCtx.fillStyle = '#f39c12';
    wheelCtx.fill();
    wheelCtx.strokeStyle = '#fff';
    wheelCtx.lineWidth = 8;
    wheelCtx.stroke();

    // رسم نقطة المركز
    wheelCtx.beginPath();
    wheelCtx.arc(centerX, centerY, 18, 0, 2 * Math.PI);
    wheelCtx.fillStyle = '#e67e22';
    wheelCtx.fill();

    // رسم خط مرجعي للمؤشر (خط أحمر من المركز إلى الأعلى)
    wheelCtx.beginPath();
    wheelCtx.moveTo(centerX, centerY);
    wheelCtx.lineTo(centerX, centerY - radius - 15);
    wheelCtx.strokeStyle = '#dc3545';
    wheelCtx.lineWidth = 5;
    wheelCtx.stroke();

    // رسم نقطة في نهاية الخط المرجعي
    wheelCtx.beginPath();
    wheelCtx.arc(centerX, centerY - radius - 15, 10, 0, 2 * Math.PI);
    wheelCtx.fillStyle = '#dc3545';
    wheelCtx.fill();
    wheelCtx.strokeStyle = '#fff';
    wheelCtx.lineWidth = 3;
    wheelCtx.stroke();
}

// تدوير العجلة
function spinWheel() {
    if (isSpinning || participants.length === 0) return;

    isSpinning = true;
    const spinBtn = document.getElementById('spin-wheel-btn');
    spinBtn.disabled = true;
    spinBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التدوير...';

    // تشغيل صوت بداية الدوران
    playWheelStartSound();

    // حساب الدوران العشوائي
    const minSpins = 5;
    const maxSpins = 8;
    const spins = Math.random() * (maxSpins - minSpins) + minSpins;
    const finalRotation = spins * 2 * Math.PI;

    // تأثير الدوران
    const duration = 3000;
    const startTime = Date.now();
    const startRotation = currentRotation;

    function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // منحنى التباطؤ
        const easeOut = 1 - Math.pow(1 - progress, 3);
        currentRotation = startRotation + finalRotation * easeOut;

        drawWheel();

        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            // انتهاء الدوران
            finishSpin();
        }
    }

    // تشغيل أصوات الدوران بعد قليل
    setTimeout(() => {
        playSpinSound();
    }, 200);

    requestAnimationFrame(animate);
}

// صوت بداية الدوران
function playWheelStartSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت انطلاق العجلة
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        const filter = audioContext.createBiquadFilter();

        oscillator.connect(filter);
        filter.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // تردد يرتفع بسرعة
        oscillator.frequency.setValueAtTime(100, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.3);

        filter.type = 'highpass';
        filter.frequency.setValueAtTime(200, audioContext.currentTime);

        gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.type = 'sawtooth';
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);

        // صوت "ووش" للانطلاق
        setTimeout(() => playWhooshSound(audioContext), 100);

    } catch (error) {
        console.log('لا يمكن تشغيل صوت البداية:', error);
    }
}

// صوت "ووش" للانطلاق
function playWhooshSound(audioContext) {
    const noiseBuffer = createNoiseBuffer(audioContext, 0.5);
    const noiseSource = audioContext.createBufferSource();
    const filter = audioContext.createBiquadFilter();
    const gainNode = audioContext.createGain();

    noiseSource.buffer = noiseBuffer;
    noiseSource.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    filter.type = 'highpass';
    filter.frequency.setValueAtTime(1000, audioContext.currentTime);
    filter.frequency.exponentialRampToValueAtTime(2000, audioContext.currentTime + 0.5);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

    noiseSource.start(audioContext.currentTime);
    noiseSource.stop(audioContext.currentTime + 0.5);
}

// إنهاء الدوران وتحديد الفائز
function finishSpin() {
    isSpinning = false;

    // تشغيل صوت توقف العجلة
    playWheelStopSound();

    // حساب الفائز بناءً على موضع المؤشر (المؤشر في الأعلى)
    const anglePerSegment = (2 * Math.PI) / participants.length;
    const normalizedRotation = (currentRotation % (2 * Math.PI) + 2 * Math.PI) % (2 * Math.PI);

    // المؤشر في الأعلى (زاوية 0)
    // نحسب أي قسم يشير إليه المؤشر
    // نبدأ من الأعلى ونتحرك في اتجاه عقارب الساعة
    const pointerAngle = (2 * Math.PI - normalizedRotation) % (2 * Math.PI);
    let winnerIndex = Math.floor(pointerAngle / anglePerSegment);

    // التأكد من أن الفهرس صحيح
    if (winnerIndex >= participants.length) {
        winnerIndex = 0;
    }
    if (winnerIndex < 0) {
        winnerIndex = participants.length - 1;
    }

    const winner = participants[winnerIndex];

    // إضافة تتبع للتشخيص
    console.log('🎯 تفاصيل حساب الفائز:');
    console.log('📊 عدد المشاركين:', participants.length);
    console.log('🔄 الدوران الحالي:', currentRotation);
    console.log('📐 الدوران المعدل:', normalizedRotation);
    console.log('🎯 زاوية المؤشر:', pointerAngle);
    console.log('📏 زاوية كل قسم:', anglePerSegment);
    console.log('🔢 فهرس الفائز:', winnerIndex);
    console.log('🏆 الفائز:', winner);
    console.log('👥 قائمة المشاركين:', participants);

    // رسم خط تشخيصي للفائز
    setTimeout(() => {
        highlightWinnerSegment(winnerIndex);
    }, 100);

    // تأثيرات النجاح
    setTimeout(() => {
        // تشغيل أصوات الفوز المحسنة
        playWheelWinSound();

        // إضافة كونفيتي
        addConfetti();

        // عرض النتيجة في واجهة القرعة
        showWheelWinner(winner);

        // إعادة تفعيل الزر
        const spinBtn = document.getElementById('spin-wheel-btn');
        spinBtn.disabled = false;
        spinBtn.innerHTML = '<i class="fas fa-sync-alt"></i> تدوير العجلة';
    }, 800);
}

// عرض فائز العجلة الدوارة
function showWheelWinner(winner) {
    // حفظ النتيجة في قاعدة البيانات
    saveWheelResult(winner);

    // إخفاء أزرار القرعة وإظهار النتيجة
    const lotterySection = document.getElementById('lottery-section');
    const resultSection = document.getElementById('result-section');
    const winnerName = document.getElementById('winner-name');
    const winnerDetails = document.getElementById('winner-details');

    if (lotterySection) lotterySection.style.display = 'none';
    if (resultSection) {
        resultSection.style.display = 'block';

        // تحديث محتوى النتيجة
        if (winnerName) {
            winnerName.textContent = winner;
            winnerName.style.color = '#17a2b8'; // لون أزرق للعجلة
            winnerName.style.textShadow = '2px 2px 4px rgba(23, 162, 184, 0.3)';
        }

        if (winnerDetails) {
            winnerDetails.innerHTML = `🎡 فائز العجلة الدوارة من بين ${participants.length} مشارك`;
        }

        // تغيير لون التنبيه
        const alertElement = resultSection.querySelector('.alert');
        if (alertElement) {
            alertElement.className = 'alert alert-info text-center';
        }

        // تحديث العنوان
        const headerElement = resultSection.querySelector('h5') || resultSection.querySelector('h4');
        if (headerElement) {
            headerElement.innerHTML = '<i class="fas fa-dharmachakra"></i> فائز العجلة الدوارة:';
        }
    }

    // إضافة تأثير خاص للعجلة
    addWheelWinnerEffect(winner);
}

// حفظ نتيجة العجلة الدوارة
function saveWheelResult(winner) {
    // إنشاء بيانات وهمية للفائز (يمكن تحسينها لاحقاً)
    const wheelData = {
        winner: {
            name: winner,
            email: null,
            phone: null
        },
        total_participants: participants.length,
        type: 'wheel'
    };

    // إرسال النتيجة للخادم
    fetch('/save_wheel_result', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(wheelData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('تم حفظ نتيجة العجلة:', data);
    })
    .catch(error => {
        console.error('خطأ في حفظ نتيجة العجلة:', error);
    });
}

// تأثيرات خاصة لفائز العجلة
function addWheelWinnerEffect(winner) {
    // إضافة توهج للعجلة
    const wheelCanvas = document.getElementById('wheel-canvas');
    if (wheelCanvas) {
        wheelCanvas.style.boxShadow = '0 0 30px rgba(23, 162, 184, 0.8)';
        wheelCanvas.style.transform = 'scale(1.05)';

        // إزالة التأثير بعد 3 ثوان
        setTimeout(() => {
            wheelCanvas.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.5)';
            wheelCanvas.style.transform = 'scale(1)';
        }, 3000);
    }

    // إضافة كونفيتي أزرق خاص بالعجلة
    for (let i = 0; i < 40; i++) {
        createWheelConfettiPiece();
    }

    // إضافة رسالة منبثقة مؤقتة
    showWheelPopup(winner);
}

// كونفيتي خاص بالعجلة
function createWheelConfettiPiece() {
    const confetti = document.createElement('div');
    confetti.style.position = 'fixed';
    confetti.style.width = '12px';
    confetti.style.height = '12px';
    confetti.style.backgroundColor = getWheelColor();
    confetti.style.left = Math.random() * 100 + 'vw';
    confetti.style.top = '-10px';
    confetti.style.zIndex = '9999';
    confetti.style.borderRadius = '50%';
    confetti.style.pointerEvents = 'none';
    confetti.style.boxShadow = '0 0 6px rgba(23, 162, 184, 0.6)';

    document.body.appendChild(confetti);

    // تحريك الكونفيتي
    const animation = confetti.animate([
        { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
        { transform: `translateY(100vh) rotate(${Math.random() * 720}deg)`, opacity: 0 }
    ], {
        duration: Math.random() * 3500 + 2500,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    });

    animation.onfinish = () => {
        confetti.remove();
    };
}

// ألوان خاصة بالعجلة
function getWheelColor() {
    const colors = ['#17a2b8', '#20c997', '#6f42c1', '#e83e8c', '#fd7e14', '#20c997'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// رسالة منبثقة للعجلة
function showWheelPopup(winner) {
    const popup = document.createElement('div');
    popup.style.position = 'fixed';
    popup.style.top = '50%';
    popup.style.left = '50%';
    popup.style.transform = 'translate(-50%, -50%)';
    popup.style.background = 'linear-gradient(135deg, #17a2b8, #138496)';
    popup.style.color = 'white';
    popup.style.padding = '30px 40px';
    popup.style.borderRadius = '20px';
    popup.style.fontSize = '24px';
    popup.style.fontWeight = 'bold';
    popup.style.textAlign = 'center';
    popup.style.zIndex = '10000';
    popup.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
    popup.style.border = '3px solid #fff';
    popup.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-dharmachakra fa-2x" style="animation: spin 2s linear infinite;"></i>
        </div>
        <div>🎉 الفائز هو 🎉</div>
        <div style="font-size: 28px; margin: 10px 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">${winner}</div>
        <div style="font-size: 16px; opacity: 0.9;">فائز العجلة الدوارة</div>
    `;

    document.body.appendChild(popup);

    // تأثير الظهور
    popup.style.opacity = '0';
    popup.style.transform = 'translate(-50%, -50%) scale(0.5)';

    setTimeout(() => {
        popup.style.transition = 'all 0.5s ease';
        popup.style.opacity = '1';
        popup.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 100);

    // إزالة الرسالة بعد 4 ثوان
    setTimeout(() => {
        popup.style.transition = 'all 0.5s ease';
        popup.style.opacity = '0';
        popup.style.transform = 'translate(-50%, -50%) scale(0.5)';
        setTimeout(() => popup.remove(), 500);
    }, 4000);
}

// أصوات العجلة الدوارة المحسنة
function playSpinSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت دوران العجلة الأساسي
        playWheelSpinningSound(audioContext);

        // أصوات النقر أثناء الدوران
        playTickingSounds(audioContext);

        // صوت الرياح أثناء الدوران
        playWindSound(audioContext);

    } catch (error) {
        console.log('لا يمكن تشغيل صوت الدوران:', error);
    }
}

// صوت دوران العجلة الأساسي
function playWheelSpinningSound(audioContext) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();

    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // تردد متغير لمحاكاة صوت الدوران
    oscillator.frequency.setValueAtTime(150, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(300, audioContext.currentTime + 1);
    oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 3);

    // فلتر لجعل الصوت أكثر واقعية
    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(800, audioContext.currentTime);
    filter.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 3);

    // تحكم في الصوت
    gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.05, audioContext.currentTime + 2);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 3);

    oscillator.type = 'sawtooth';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 3);
}

// أصوات النقر أثناء مرور العجلة على الأقسام
function playTickingSounds(audioContext) {
    const tickCount = 20; // عدد النقرات
    const tickInterval = 150; // الفترة بين النقرات بالميلي ثانية

    for (let i = 0; i < tickCount; i++) {
        setTimeout(() => {
            playTickSound(audioContext, i);
        }, i * tickInterval);
    }
}

// صوت نقرة واحدة
function playTickSound(audioContext, index) {
    try {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // تردد عالي للنقرة
        oscillator.frequency.setValueAtTime(800 + (index * 10), audioContext.currentTime);

        // نقرة قصيرة وحادة
        gainNode.gain.setValueAtTime(0.08, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);

        oscillator.type = 'square';
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.05);
    } catch (error) {
        // تجاهل الأخطاء للنقرات الفردية
    }
}

// صوت الرياح أثناء الدوران
function playWindSound(audioContext) {
    const noiseBuffer = createNoiseBuffer(audioContext, 3);
    const noiseSource = audioContext.createBufferSource();
    const filter = audioContext.createBiquadFilter();
    const gainNode = audioContext.createGain();

    noiseSource.buffer = noiseBuffer;
    noiseSource.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // فلتر للحصول على صوت رياح
    filter.type = 'bandpass';
    filter.frequency.setValueAtTime(200, audioContext.currentTime);
    filter.Q.setValueAtTime(0.5, audioContext.currentTime);

    // تحكم في مستوى الصوت
    gainNode.gain.setValueAtTime(0.03, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.08, audioContext.currentTime + 1);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 3);

    noiseSource.start(audioContext.currentTime);
    noiseSource.stop(audioContext.currentTime + 3);
}

// إنشاء buffer للضوضاء البيضاء
function createNoiseBuffer(audioContext, duration) {
    const sampleRate = audioContext.sampleRate;
    const bufferSize = sampleRate * duration;
    const buffer = audioContext.createBuffer(1, bufferSize, sampleRate);
    const data = buffer.getChannelData(0);

    for (let i = 0; i < bufferSize; i++) {
        data[i] = Math.random() * 2 - 1;
    }

    return buffer;
}

// صوت توقف العجلة
function playWheelStopSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // صوت توقف تدريجي
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        const filter = audioContext.createBiquadFilter();

        oscillator.connect(filter);
        filter.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // تردد ينخفض تدريجياً
        oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(50, audioContext.currentTime + 0.8);

        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(1000, audioContext.currentTime);
        filter.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.8);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);

        oscillator.type = 'sawtooth';
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.8);

    } catch (error) {
        console.log('لا يمكن تشغيل صوت التوقف:', error);
    }
}

// صوت فوز العجلة المحسن
function playWheelWinSound() {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // لحن الفوز
        playVictoryMelody(audioContext);

        // أصوات الاحتفال
        setTimeout(() => playCheerSound(audioContext), 500);

        // صوت الجرس
        setTimeout(() => playBellSound(audioContext), 1000);

    } catch (error) {
        console.log('لا يمكن تشغيل صوت الفوز:', error);
    }
}

// لحن الفوز
function playVictoryMelody(audioContext) {
    const notes = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
    const noteDuration = 0.3;

    notes.forEach((frequency, index) => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + noteDuration);

            oscillator.type = 'sine';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + noteDuration);
        }, index * 200);
    });
}

// صوت الاحتفال
function playCheerSound(audioContext) {
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(400 + Math.random() * 400, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.type = 'triangle';
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }, i * 100);
    }
}

// صوت الجرس
function playBellSound(audioContext) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();

    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);

    filter.type = 'peaking';
    filter.frequency.setValueAtTime(800, audioContext.currentTime);
    filter.Q.setValueAtTime(10, audioContext.currentTime);
    filter.gain.setValueAtTime(20, audioContext.currentTime);

    gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 2);

    oscillator.type = 'sine';
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 2);
}

// تمييز القسم الفائز بصرياً
function highlightWinnerSegment(winnerIndex) {
    if (!wheelCtx || participants.length === 0) return;

    const centerX = wheelCanvas.width / 2;
    const centerY = wheelCanvas.height / 2;
    const radius = 250;
    const anglePerSegment = (2 * Math.PI) / participants.length;

    // حساب زوايا القسم الفائز
    const startAngle = winnerIndex * anglePerSegment + currentRotation;
    const endAngle = (winnerIndex + 1) * anglePerSegment + currentRotation;

    // رسم إطار ذهبي حول القسم الفائز
    wheelCtx.beginPath();
    wheelCtx.moveTo(centerX, centerY);
    wheelCtx.arc(centerX, centerY, radius + 8, startAngle, endAngle);
    wheelCtx.closePath();
    wheelCtx.strokeStyle = '#ffd700';
    wheelCtx.lineWidth = 8;
    wheelCtx.stroke();

    // رسم خط من المركز إلى منتصف القسم الفائز
    const midAngle = startAngle + anglePerSegment / 2;
    const lineEndX = centerX + Math.cos(midAngle) * (radius + 15);
    const lineEndY = centerY + Math.sin(midAngle) * (radius + 15);

    wheelCtx.beginPath();
    wheelCtx.moveTo(centerX, centerY);
    wheelCtx.lineTo(lineEndX, lineEndY);
    wheelCtx.strokeStyle = '#ffd700';
    wheelCtx.lineWidth = 6;
    wheelCtx.stroke();

    // رسم نجمة في نهاية الخط
    drawStar(lineEndX, lineEndY, 15, '#ffd700');

    console.log('🌟 تم تمييز القسم الفائز:', winnerIndex, 'للمشارك:', participants[winnerIndex]);
}

// رسم نجمة
function drawStar(x, y, size, color) {
    const spikes = 5;
    const outerRadius = size;
    const innerRadius = size * 0.4;

    wheelCtx.save();
    wheelCtx.translate(x, y);
    wheelCtx.beginPath();

    for (let i = 0; i < spikes * 2; i++) {
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const angle = (i * Math.PI) / spikes;
        const starX = Math.cos(angle) * radius;
        const starY = Math.sin(angle) * radius;

        if (i === 0) {
            wheelCtx.moveTo(starX, starY);
        } else {
            wheelCtx.lineTo(starX, starY);
        }
    }

    wheelCtx.closePath();
    wheelCtx.fillStyle = color;
    wheelCtx.fill();
    wheelCtx.strokeStyle = '#fff';
    wheelCtx.lineWidth = 2;
    wheelCtx.stroke();
    wheelCtx.restore();
}
