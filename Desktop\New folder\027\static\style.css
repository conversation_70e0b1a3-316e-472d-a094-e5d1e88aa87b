/* تنسيق عام */
body {
    font-family: 'Se<PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    padding: 0;
}

/* Header */
header {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: bold;
    border: none;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.2rem;
}

/* Form inputs */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Participant items */
.participant-item {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6 !important;
    transition: all 0.3s ease;
}

.participant-item:hover {
    background: linear-gradient(45deg, #e9ecef, #dee2e6);
    transform: translateX(-5px);
}

/* Loading animation */
#loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Winner announcement */
#result-section .alert {
    border-radius: 15px;
    border: none;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

#winner-name {
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    animation: bounce 1s ease-out;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Participants list */
.participants-list {
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.participants-list::-webkit-scrollbar {
    width: 8px;
}

.participants-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.participants-list::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 10px;
}

.participants-list::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Table styling */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background: linear-gradient(45deg, #343a40, #495057);
    color: white;
    font-weight: bold;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #dee2e6;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Badges */
.badge {
    border-radius: 20px;
    font-size: 0.9rem;
    padding: 8px 12px;
}

/* Icons */
.fas, .far {
    margin-left: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    header h1 {
        font-size: 1.8rem;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .btn-lg {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

/* Footer */
footer {
    background: rgba(248, 249, 250, 0.8);
    margin-top: 50px;
}

/* Custom animations for draw button */
.btn-warning:hover {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Success messages */
.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    border: 2px solid #b8dacc;
}

/* Statistics cards */
.bg-light {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef) !important;
}

.card-body h2 {
    font-size: 2.5rem;
    font-weight: bold;
}
