/* تنسيق عام */
body {
    font-family: 'Se<PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    padding: 0;
}

/* Header */
header {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    font-weight: bold;
    border: none;
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-lg {
    padding: 15px 30px;
    font-size: 1.2rem;
}

/* Form inputs */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Participant items */
.participant-item {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6 !important;
    transition: all 0.3s ease;
}

.participant-item:hover {
    background: linear-gradient(45deg, #e9ecef, #dee2e6);
    transform: translateX(-5px);
}

/* Loading animation */
#loading {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Winner announcement */
#result-section .alert {
    border-radius: 15px;
    border: none;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

#winner-name {
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    animation: bounce 1s ease-out;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Participants list */
.participants-list {
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.participants-list::-webkit-scrollbar {
    width: 8px;
}

.participants-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.participants-list::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 10px;
}

.participants-list::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Table styling */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background: linear-gradient(45deg, #343a40, #495057);
    color: white;
    font-weight: bold;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #dee2e6;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Badges */
.badge {
    border-radius: 20px;
    font-size: 0.9rem;
    padding: 8px 12px;
}

/* Icons */
.fas, .far {
    margin-left: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
    header h1 {
        font-size: 1.8rem;
    }

    .card {
        margin-bottom: 20px;
    }

    .btn-lg {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

/* Footer */
footer {
    background: rgba(248, 249, 250, 0.8);
    margin-top: 50px;
}

/* Custom animations for draw button */
.btn-warning:hover {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Urgent lottery button styling */
.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    position: relative;
    overflow: hidden;
}

.btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    animation: urgentPulse 0.6s ease-in-out infinite;
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
}

@keyframes urgentPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(220, 53, 69, 0.8);
    }
}

/* Urgent button lightning effect */
.btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-danger:hover::before {
    left: 100%;
}

/* Success messages */
.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    border: 2px solid #b8dacc;
}

/* Statistics cards */
.bg-light {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef) !important;
}

.card-body h2 {
    font-size: 2.5rem;
    font-weight: bold;
}

/* تنسيقات الأزرار الجانبية */
.d-grid .btn-lg {
    min-height: 80px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.d-grid .btn-lg i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.d-grid .btn-lg:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* تنسيقات الأزرار المتوسطة الجديدة */
.d-grid .btn-md {
    min-height: 50px;
    font-size: 1rem;
    font-weight: bold;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 8px 16px;
}

.d-grid .btn-md i {
    font-size: 1.1rem;
    margin-right: 6px;
}

.d-grid .btn-md:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* تأثيرات خاصة لزر القرعة العادية */
.d-grid .btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

.d-grid .btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    color: #212529;
}

/* تأثيرات خاصة لزر القرعة العاجلة */
.d-grid .btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.d-grid .btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
}

/* تأثيرات خاصة للأزرار المتوسطة */
.d-grid .btn-md.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
    box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
}

.d-grid .btn-md.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    color: #212529;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
}

.d-grid .btn-md.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    color: white;
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
}

.d-grid .btn-md.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    color: white;
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* عداد المشاركين */
.badge.fs-4 {
    font-size: 1.8rem !important;
    border-radius: 50px;
    min-width: 60px;
    min-height: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

/* عداد المشاركين المتوسط */
.badge.fs-5 {
    font-size: 1.4rem !important;
    border-radius: 30px;
    min-width: 45px;
    min-height: 45px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 12px rgba(13, 110, 253, 0.25);
}

/* الشريط العلوي للمشاركين */
.participants-horizontal {
    background: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #dee2e6;
}

.participants-horizontal .badge {
    font-size: 0.85rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    cursor: default;
}

.participants-horizontal .badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.participants-horizontal .badge a {
    transition: opacity 0.3s ease;
}

.participants-horizontal .badge a:hover {
    opacity: 0.7;
}

/* تنسيقات النماذج الأفقية */
.d-flex.gap-2 .form-control {
    min-width: 120px;
}

.d-flex.gap-2 .btn {
    white-space: nowrap;
    min-width: 80px;
}

/* تحسينات للشريط العلوي */
.card-header.bg-light {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border-bottom: 2px solid #dee2e6;
}

.card-body.py-3 {
    background: rgba(255, 255, 255, 0.8);
}

/* تأثيرات الأزرار في الشريط العلوي */
.btn-outline-danger {
    transition: all 0.3s ease;
}

.btn-outline-danger:hover {
    transform: scale(1.05);
}

/* تأثيرات العجلة الدوارة الخاصة */
.wheel-winner-glow {
    box-shadow: 0 0 30px rgba(23, 162, 184, 0.8) !important;
    transform: scale(1.05) !important;
}

/* الرسالة المنبثقة للعجلة */
.wheel-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    padding: 30px 40px;
    border-radius: 20px;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    z-index: 10000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 3px solid #fff;
    animation: popupAppear 0.5s ease;
}

@keyframes popupAppear {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes popupDisappear {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
}

/* أيقونة دوارة للعجلة */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-dharmachakra.spinning {
    animation: spin 2s linear infinite;
}

/* تأثيرات الكونفيتي الخاص بالعجلة */
.wheel-confetti {
    position: fixed;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    box-shadow: 0 0 6px rgba(23, 162, 184, 0.6);
}

/* تحسينات لنتيجة العجلة */
.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-color: #17a2b8;
    color: #0c5460;
}

.alert-info .fas {
    color: #17a2b8;
}

/* تأثيرات خاصة للنص */
.wheel-winner-text {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    text-shadow: none;
}

/* العجلة الدوارة */
.wheel-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px auto;
}

#wheel-canvas {
    border-radius: 50%;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 15px solid #6c757d;
    max-width: 100%;
    height: auto;
}

.wheel-pointer {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 35px solid transparent;
    border-right: 35px solid transparent;
    border-top: 60px solid #dc3545;
    z-index: 10;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.7));
}

.wheel-pointer::after {
    content: '';
    position: absolute;
    top: -57px;
    left: -26px;
    width: 52px;
    height: 52px;
    background: #dc3545;
    border-radius: 50%;
    border: 8px solid #fff;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

.wheel-pointer::before {
    content: '';
    position: absolute;
    top: -42px;
    left: -12px;
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: 50%;
    z-index: 1;
}

/* تأثيرات دوران العجلة */
.wheel-spinning {
    animation: wheelSpin 3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes wheelSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(1800deg); }
}

.wheel-slow-spin {
    animation: wheelSlowSpin 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes wheelSlowSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(720deg); }
}

/* تأثيرات زر تدوير العجلة */
#spin-wheel-btn {
    background: linear-gradient(45deg, #17a2b8, #138496);
    border: none;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: bold;
    transition: all 0.3s ease;
}

#spin-wheel-btn:hover {
    background: linear-gradient(45deg, #138496, #117a8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

#spin-wheel-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* تحسينات للأقسام المضغوطة */
.form-control-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card-body.p-2 {
    padding: 0.5rem !important;
}

.participant-item {
    font-size: 0.875rem;
    padding: 0.25rem !important;
    margin-bottom: 0.25rem !important;
}

.participant-item small {
    font-size: 0.75rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    #wheel-canvas {
        width: 400px;
        height: 400px;
    }

    .wheel-pointer {
        border-left-width: 25px;
        border-right-width: 25px;
        border-top-width: 40px;
        top: -20px;
    }

    .wheel-pointer::after {
        width: 35px;
        height: 35px;
        left: -17px;
        top: -37px;
    }

    .wheel-pointer::before {
        width: 16px;
        height: 16px;
        left: -8px;
        top: -28px;
    }

    /* تخطيط الشاشات الصغيرة */
    .col-lg-3, .col-lg-8, .col-lg-2 {
        margin-bottom: 1rem;
    }

    /* الأزرار في الشاشات الصغيرة */
    .d-grid .btn-lg {
        min-height: 70px;
        font-size: 1rem;
    }

    .d-grid .btn-lg i {
        font-size: 1.3rem;
    }

    /* ترتيب عمودي للشاشات الصغيرة - الأزرار أولاً ثم العجلة */
    .col-md-4 {
        order: 0;
        margin-bottom: 2rem;
    }

    .col-md-8 {
        order: 1;
    }

    /* تحسين الأزرار في الشاشات الصغيرة */
    .d-grid .btn-md {
        min-height: 45px;
        font-size: 0.95rem;
    }

    .d-grid .btn-md i {
        font-size: 1rem;
    }

    /* الشريط العلوي في الشاشات الصغيرة */
    .card-header .row {
        text-align: center !important;
    }

    .card-header .col-md-6 {
        margin-bottom: 0.5rem;
    }

    .card-body .row .col-md-6 {
        margin-bottom: 1rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .d-flex.gap-2 .form-control {
        min-width: auto;
    }

    .participants-horizontal {
        max-height: 120px !important;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (max-width: 992px) {
    #wheel-canvas {
        width: 480px;
        height: 480px;
    }

    .d-grid .btn-lg {
        min-height: 75px;
    }

    .wheel-pointer {
        border-left-width: 30px;
        border-right-width: 30px;
        border-top-width: 50px;
        top: -25px;
    }

    .wheel-pointer::after {
        width: 45px;
        height: 45px;
        left: -22px;
        top: -47px;
    }

    .wheel-pointer::before {
        width: 20px;
        height: 20px;
        left: -10px;
        top: -35px;
    }
}
