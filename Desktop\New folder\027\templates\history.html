<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تاريخ القرعات - برنامج القرعة الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="text-center py-4 mb-4">
            <h1><i class="fas fa-history"></i> تاريخ القرعات</h1>
            <a href="/" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i> العودة للصفحة الرئيسية
            </a>
        </header>

        <!-- Results Table -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-trophy"></i> نتائج القرعات السابقة</h5>
            </div>
            <div class="card-body">
                {% if results %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>#</th>
                                    <th>اسم الفائز</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>رقم الهاتف</th>
                                    <th>عدد المشاركين</th>
                                    <th>تاريخ القرعة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in results %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>
                                            <strong class="text-success">
                                                <i class="fas fa-crown"></i> {{ result[1] }}
                                            </strong>
                                        </td>
                                        <td>
                                            {% if result[2] %}
                                                <i class="fas fa-envelope"></i> {{ result[2] }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if result[3] %}
                                                <i class="fas fa-phone"></i> {{ result[3] }}
                                            {% else %}
                                                <span class="text-muted">غير محدد</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ result[4] }} مشارك</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar"></i> {{ result[5] }}
                                            </small>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Statistics -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">
                                        <i class="fas fa-chart-bar text-primary"></i> إجمالي القرعات
                                    </h5>
                                    <h2 class="text-primary">{{ results|length }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">
                                        <i class="fas fa-users text-success"></i> متوسط المشاركين
                                    </h5>
                                    <h2 class="text-success">
                                        {% set total_participants = results|sum(attribute=4) %}
                                        {{ "%.1f"|format(total_participants / results|length) if results|length > 0 else 0 }}
                                    </h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">
                                        <i class="fas fa-calendar text-warning"></i> آخر قرعة
                                    </h5>
                                    <p class="text-warning mb-0">
                                        {% if results %}
                                            {{ results[0][5] }}
                                        {% else %}
                                            لا توجد قرعات
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد قرعات سابقة</h4>
                        <p class="text-muted">لم يتم إجراء أي قرعات حتى الآن</p>
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إجراء قرعة جديدة
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
