<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج القرعة الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <header class="text-center py-4 mb-4 bg-primary text-white">
                    <h1><i class="fas fa-gift"></i> برنامج القرعة الإلكترونية</h1>
                    <p class="mb-0">أضف المشاركين واسحب القرعة بسهولة</p>
                </header>
            </div>
        </div>

        <div class="row">
            <!-- قسم إضافة المشاركين -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-user-plus"></i> إضافة مشارك جديد</h5>
                    </div>
                    <div class="card-body">
                        <form action="/add_participant" method="POST">
                            <div class="mb-3">
                                <label for="name" class="form-label">الاسم *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-plus"></i> إضافة مشارك
                            </button>
                        </form>

                        <hr>

                        <!-- إضافة عدة مشاركين -->
                        <h6>إضافة عدة مشاركين</h6>
                        <form action="/add_multiple" method="POST">
                            <div class="mb-3">
                                <label for="names_text" class="form-label">الأسماء (كل اسم في سطر منفصل)</label>
                                <textarea class="form-control" id="names_text" name="names_text" rows="4" 
                                          placeholder="أحمد محمد&#10;فاطمة علي&#10;خالد سعد"></textarea>
                            </div>
                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-users"></i> إضافة عدة مشاركين
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قسم القرعة -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-dice"></i> إجراء القرعة</h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="lottery-section">
                            <p class="text-muted">عدد المشاركين: <span class="badge bg-primary">{{ participants|length }}</span></p>
                            
                            {% if participants %}
                                <button id="draw-btn" class="btn btn-warning btn-lg mb-3" onclick="drawLottery()">
                                    <i class="fas fa-magic"></i> اسحب القرعة
                                </button>
                            {% else %}
                                <p class="text-muted">يجب إضافة مشاركين أولاً</p>
                            {% endif %}
                        </div>

                        <!-- نتيجة القرعة -->
                        <div id="result-section" style="display: none;">
                            <div class="alert alert-success">
                                <h4><i class="fas fa-trophy"></i> الفائز هو:</h4>
                                <h2 id="winner-name" class="text-primary"></h2>
                                <p id="winner-details"></p>
                            </div>
                            <button class="btn btn-secondary" onclick="resetLottery()">
                                <i class="fas fa-redo"></i> إجراء قرعة جديدة
                            </button>
                        </div>

                        <!-- Loading animation -->
                        <div id="loading" style="display: none;">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">جاري السحب...</span>
                            </div>
                            <p class="mt-2">جاري سحب القرعة...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة المشاركين -->
            <div class="col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> المشاركون ({{ participants|length }})</h5>
                        {% if participants %}
                            <a href="/clear_all" class="btn btn-sm btn-danger" 
                               onclick="return confirm('هل أنت متأكد من حذف جميع المشاركين؟')">
                                <i class="fas fa-trash"></i> مسح الكل
                            </a>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if participants %}
                            <div class="participants-list" style="max-height: 400px; overflow-y: auto;">
                                {% for participant in participants %}
                                    <div class="participant-item d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                                        <div>
                                            <strong>{{ participant[1] }}</strong>
                                            {% if participant[2] %}
                                                <br><small class="text-muted">{{ participant[2] }}</small>
                                            {% endif %}
                                            {% if participant[3] %}
                                                <br><small class="text-muted">{{ participant[3] }}</small>
                                            {% endif %}
                                        </div>
                                        <a href="/delete_participant/{{ participant[0] }}" 
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف {{ participant[1] }}؟')">
                                            <i class="fas fa-times"></i>
                                        </a>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted text-center">لا يوجد مشاركين حتى الآن</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row">
            <div class="col-12">
                <footer class="text-center py-3 mt-4 border-top">
                    <p class="text-muted">
                        <a href="/history" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> تاريخ القرعات
                        </a>
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
