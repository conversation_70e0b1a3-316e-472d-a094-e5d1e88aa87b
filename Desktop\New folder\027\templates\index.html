<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج القرعة الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <header class="text-center py-4 mb-4 bg-primary text-white">
                    <h1><i class="fas fa-gift"></i> برنامج القرعة الإلكترونية</h1>
                    <p class="mb-0">أضف المشاركين واسحب القرعة بسهولة</p>
                </header>
            </div>
        </div>

        <!-- الشريط العلوي للمشاركين -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0"><i class="fas fa-user-plus text-success"></i> إضافة مشارك جديد</h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <h5 class="mb-0">
                                    <i class="fas fa-list text-info"></i> المشاركون
                                    <span class="badge bg-primary ms-2">{{ participants|length }}</span>
                                    {% if participants %}
                                        <a href="/clear_all" class="btn btn-sm btn-outline-danger ms-2"
                                           onclick="return confirm('هل أنت متأكد من حذف جميع المشاركين؟')">
                                            <i class="fas fa-trash"></i> مسح الكل
                                        </a>
                                    {% endif %}
                                </h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-body py-3">
                        <div class="row">
                            <!-- قسم إضافة المشاركين -->
                            <div class="col-lg-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <form action="/add_participant" method="POST" class="d-flex gap-2 align-items-end" onsubmit="handleFormSubmit(this)">
                                            <div class="flex-fill">
                                                <input type="text" class="form-control form-control-sm" name="name"
                                                       placeholder="الاسم *" required>
                                            </div>
                                            <div class="flex-fill">
                                                <input type="email" class="form-control form-control-sm" name="email"
                                                       placeholder="البريد الإلكتروني">
                                            </div>
                                            <div class="flex-fill">
                                                <input type="tel" class="form-control form-control-sm" name="phone"
                                                       placeholder="رقم الهاتف">
                                            </div>
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-plus"></i> إضافة
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-6">
                                        <form action="/add_multiple" method="POST" class="d-flex gap-2 align-items-end" onsubmit="handleFormSubmit(this)">
                                            <div class="flex-fill">
                                                <textarea class="form-control form-control-sm" name="names_text" rows="1"
                                                          placeholder="أسماء متعددة (مفصولة بفواصل)"></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-info btn-sm">
                                                <i class="fas fa-users"></i> إضافة متعددة
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم عرض المشاركين -->
                            <div class="col-lg-6">
                                <div class="participants-horizontal" style="max-height: 80px; overflow-y: auto;">
                                    {% if participants %}
                                        <div class="d-flex flex-wrap gap-1">
                                            {% for participant in participants %}
                                                <span class="badge bg-secondary d-flex align-items-center gap-1 p-2">
                                                    <small>{{ participant[1] }}</small>
                                                    <a href="/delete_participant/{{ participant[0] }}"
                                                       class="text-white text-decoration-none"
                                                       onclick="return confirm('هل أنت متأكد من حذف {{ participant[1] }}؟')">
                                                        <i class="fas fa-times" style="font-size: 0.7rem;"></i>
                                                    </a>
                                                </span>
                                            {% endfor %}
                                        </div>
                                    {% else %}
                                        <p class="text-muted text-center small mb-0">لا يوجد مشاركين حتى الآن</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- قسم القرعة -->
            <div class="col-12 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h4><i class="fas fa-dice"></i> إجراء القرعة</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- الأزرار والنتائج على اليسار -->
                            <div class="col-md-4">
                                <div class="d-flex flex-column h-100 justify-content-center">
                                    <!-- عداد المشاركين -->
                                    <div class="text-center mb-3">
                                        <h6 class="text-muted">عدد المشاركين</h6>
                                        <span class="badge bg-primary fs-5 px-2 py-1">{{ participants|length }}</span>
                                    </div>

                                    <!-- أزرار القرعة -->
                                    <div id="lottery-section">
                                        {% if participants %}
                                            <div class="d-grid gap-2">
                                                <!-- زر اختيار المتسابقين -->
                                                <div class="dropdown">
                                                    <button class="btn btn-info btn-md py-2 dropdown-toggle w-100" type="button" id="selectParticipantsBtn" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fas fa-users-cog"></i>
                                                        <span class="ms-2">اختيار المتسابقين</span>
                                                    </button>
                                                    <ul class="dropdown-menu w-100" aria-labelledby="selectParticipantsBtn">
                                                        <li><a class="dropdown-item" href="#" onclick="selectAllParticipants()">
                                                            <i class="fas fa-check-double text-success"></i> اختيار الكل
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="selectRandomParticipants()">
                                                            <i class="fas fa-random text-primary"></i> اختيار عشوائي
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="selectByNumber()">
                                                            <i class="fas fa-hashtag text-info"></i> اختيار بالعدد
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="selectManually()">
                                                            <i class="fas fa-hand-pointer text-warning"></i> اختيار يدوي
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="clearSelection()">
                                                            <i class="fas fa-times"></i> إلغاء الاختيار
                                                        </a></li>
                                                    </ul>
                                                </div>

                                                <button id="draw-btn" class="btn btn-warning btn-md py-2" onclick="drawLottery()">
                                                    <i class="fas fa-magic"></i>
                                                    <span class="ms-2">اسحب القرعة</span>
                                                </button>

                                                <button id="draw-urgent-btn" class="btn btn-danger btn-md py-2" onclick="drawLotteryUrgent()">
                                                    <i class="fas fa-bolt"></i>
                                                    <span class="ms-2">قرعة عاجلة</span>
                                                </button>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- نتيجة القرعة -->
                                    <div id="result-section" style="display: none;">
                                        <div class="alert alert-success text-center">
                                            <h6><i class="fas fa-trophy"></i> الفائز هو:</h6>
                                            <h4 id="winner-name" class="text-primary mb-2"></h4>
                                            <p id="winner-details" class="mb-3"></p>
                                            <button class="btn btn-secondary btn-sm" onclick="resetLottery()">
                                                <i class="fas fa-redo"></i> قرعة جديدة
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Loading animation -->
                                    <div id="loading" style="display: none;" class="text-center">
                                        <div class="spinner-border text-warning mb-2" role="status">
                                            <span class="visually-hidden">جاري السحب...</span>
                                        </div>
                                        <p class="text-muted small">جاري سحب القرعة...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- العجلة الدوارة على اليمين -->
                            <div class="col-md-8">
                                <div id="wheel-container" style="display: {% if participants %}block{% else %}none{% endif %};">
                                    <h5 class="text-center mb-4">
                                        <i class="fas fa-dharmachakra"></i> العجلة الدوارة
                                    </h5>
                                    <div class="wheel-wrapper">
                                        <canvas id="wheel-canvas" width="550" height="550"></canvas>
                                        <div class="wheel-pointer"></div>
                                    </div>
                                    <div class="text-center mt-4">
                                        <button id="spin-wheel-btn" class="btn btn-info btn-lg" onclick="spinWheel()">
                                            <i class="fas fa-sync-alt"></i> تدوير العجلة
                                        </button>
                                    </div>
                                </div>

                                <!-- رسالة عدم وجود مشاركين -->
                                {% if not participants %}
                                    <div class="text-center py-5">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">يجب إضافة مشاركين أولاً</h5>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row">
            <div class="col-12">
                <footer class="text-center py-3 mt-4 border-top">
                    <p class="text-muted">
                        <a href="/history" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> تاريخ القرعات
                        </a>
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <!-- نافذة اختيار المتسابقين -->
    <div class="modal fade" id="participantSelectionModal" tabindex="-1" aria-labelledby="participantSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="participantSelectionModalLabel">
                        <i class="fas fa-users-cog"></i> اختيار المتسابقين
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-list"></i> جميع المشاركين:</h6>
                            <div id="allParticipantsList" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle text-success"></i> المتسابقين المختارين:</h6>
                            <div id="selectedParticipantsList" class="border rounded p-3" style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                <p class="text-muted text-center">لم يتم اختيار أي متسابق</p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="randomCount" class="form-label">عدد المتسابقين العشوائي:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="randomCount" min="1" max="{{ participants|length }}" value="5">
                                <button class="btn btn-outline-primary" type="button" onclick="selectRandomCount()">
                                    <i class="fas fa-random"></i> اختيار
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إجراءات سريعة:</label>
                            <div class="d-grid gap-2">
                                <button class="btn btn-success btn-sm" onclick="selectAllInModal()">
                                    <i class="fas fa-check-double"></i> اختيار الكل
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="clearAllInModal()">
                                    <i class="fas fa-times"></i> إلغاء الكل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="me-auto">
                        <span class="badge bg-info" id="selectedCount">0</span> متسابق مختار
                    </div>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="applySelection()">
                        <i class="fas fa-check"></i> تطبيق الاختيار
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
