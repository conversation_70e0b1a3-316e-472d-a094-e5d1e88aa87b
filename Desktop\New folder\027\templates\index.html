<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>برنامج القرعة الإلكترونية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Header -->
            <div class="col-12">
                <header class="text-center py-4 mb-4 bg-primary text-white">
                    <h1><i class="fas fa-gift"></i> برنامج القرعة الإلكترونية</h1>
                    <p class="mb-0">أضف المشاركين واسحب القرعة بسهولة</p>
                </header>
            </div>
        </div>

        <div class="row">
            <!-- قسم إضافة المشاركين -->
            <div class="col-lg-3 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h6><i class="fas fa-user-plus"></i> إضافة مشارك جديد</h6>
                    </div>
                    <div class="card-body">
                        <form action="/add_participant" method="POST">
                            <div class="mb-2">
                                <label for="name" class="form-label small">الاسم *</label>
                                <input type="text" class="form-control form-control-sm" id="name" name="name" required>
                            </div>
                            <div class="mb-2">
                                <label for="email" class="form-label small">البريد الإلكتروني</label>
                                <input type="email" class="form-control form-control-sm" id="email" name="email">
                            </div>
                            <div class="mb-2">
                                <label for="phone" class="form-label small">رقم الهاتف</label>
                                <input type="tel" class="form-control form-control-sm" id="phone" name="phone">
                            </div>
                            <button type="submit" class="btn btn-success btn-sm w-100 mb-2">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </form>

                        <hr class="my-2">

                        <!-- إضافة عدة مشاركين -->
                        <h6 class="small">إضافة عدة مشاركين</h6>
                        <form action="/add_multiple" method="POST">
                            <div class="mb-2">
                                <textarea class="form-control form-control-sm" id="names_text" name="names_text" rows="3"
                                          placeholder="أحمد محمد&#10;فاطمة علي&#10;خالد سعد"></textarea>
                            </div>
                            <button type="submit" class="btn btn-info btn-sm w-100">
                                <i class="fas fa-users"></i> إضافة متعددة
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قسم القرعة -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-dice"></i> إجراء القرعة</h5>
                    </div>
                    <div class="card-body text-center">
                        <div id="lottery-section">
                            <p class="text-muted">عدد المشاركين: <span class="badge bg-primary">{{ participants|length }}</span></p>

                            {% if participants %}
                                <button id="draw-btn" class="btn btn-warning btn-lg mb-3" onclick="drawLottery()">
                                    <i class="fas fa-magic"></i> اسحب القرعة
                                </button>
                                <br>
                                <button id="draw-urgent-btn" class="btn btn-danger btn-lg mb-3" onclick="drawLotteryUrgent()">
                                    <i class="fas fa-bolt"></i> قرعة عاجلة
                                </button>
                            {% else %}
                                <p class="text-muted">يجب إضافة مشاركين أولاً</p>
                            {% endif %}
                        </div>

                        <!-- نتيجة القرعة -->
                        <div id="result-section" style="display: none;">
                            <div class="alert alert-success">
                                <h4><i class="fas fa-trophy"></i> الفائز هو:</h4>
                                <h2 id="winner-name" class="text-primary"></h2>
                                <p id="winner-details"></p>
                            </div>
                            <button class="btn btn-secondary" onclick="resetLottery()">
                                <i class="fas fa-redo"></i> إجراء قرعة جديدة
                            </button>
                        </div>

                        <!-- Loading animation -->
                        <div id="loading" style="display: none;">
                            <div class="spinner-border text-warning" role="status">
                                <span class="visually-hidden">جاري السحب...</span>
                            </div>
                            <p class="mt-2">جاري سحب القرعة...</p>
                        </div>

                        <!-- العجلة الدوارة -->
                        <div id="wheel-container" class="mt-4" style="display: {% if participants %}block{% else %}none{% endif %};">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-dharmachakra"></i> العجلة الدوارة
                            </h6>
                            <div class="wheel-wrapper">
                                <canvas id="wheel-canvas" width="300" height="300"></canvas>
                                <div class="wheel-pointer"></div>
                            </div>
                            <div class="text-center mt-3">
                                <button id="spin-wheel-btn" class="btn btn-info btn-sm" onclick="spinWheel()">
                                    <i class="fas fa-sync-alt"></i> تدوير العجلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة المشاركين -->
            <div class="col-lg-3 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h6><i class="fas fa-list"></i> المشاركون ({{ participants|length }})</h6>
                        {% if participants %}
                            <a href="/clear_all" class="btn btn-sm btn-danger btn-sm"
                               onclick="return confirm('هل أنت متأكد من حذف جميع المشاركين؟')">
                                <i class="fas fa-trash"></i>
                            </a>
                        {% endif %}
                    </div>
                    <div class="card-body p-2">
                        {% if participants %}
                            <div class="participants-list" style="max-height: 350px; overflow-y: auto;">
                                {% for participant in participants %}
                                    <div class="participant-item d-flex justify-content-between align-items-center mb-1 p-1 border rounded">
                                        <div class="flex-grow-1">
                                            <small><strong>{{ participant[1] }}</strong></small>
                                            {% if participant[2] %}
                                                <br><small class="text-muted" style="font-size: 0.7rem;">{{ participant[2] }}</small>
                                            {% endif %}
                                            {% if participant[3] %}
                                                <br><small class="text-muted" style="font-size: 0.7rem;">{{ participant[3] }}</small>
                                            {% endif %}
                                        </div>
                                        <a href="/delete_participant/{{ participant[0] }}"
                                           class="btn btn-sm btn-outline-danger btn-sm"
                                           onclick="return confirm('هل أنت متأكد من حذف {{ participant[1] }}؟')">
                                            <i class="fas fa-times" style="font-size: 0.7rem;"></i>
                                        </a>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted text-center small">لا يوجد مشاركين حتى الآن</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row">
            <div class="col-12">
                <footer class="text-center py-3 mt-4 border-top">
                    <p class="text-muted">
                        <a href="/history" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> تاريخ القرعات
                        </a>
                    </p>
                </footer>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
