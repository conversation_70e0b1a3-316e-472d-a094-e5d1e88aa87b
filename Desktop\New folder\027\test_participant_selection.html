<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر اختيار المتسابقين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug"></i> اختبار زر اختيار المتسابقين
        </h1>
        
        <!-- حالة النظام -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>حالة النظام:</h3>
                <ul class="list-unstyled">
                    <li><span class="status-indicator" id="bootstrapStatus"></span> Bootstrap: <span id="bootstrapText">فحص...</span></li>
                    <li><span class="status-indicator" id="participantsStatus"></span> المشاركين: <span id="participantsText">فحص...</span></li>
                    <li><span class="status-indicator" id="buttonStatus"></span> الزر: <span id="buttonText">فحص...</span></li>
                    <li><span class="status-indicator" id="functionsStatus"></span> الوظائف: <span id="functionsText">فحص...</span></li>
                </ul>
            </div>
        </div>

        <!-- زر الاختبار -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>زر اختيار المتسابقين:</h3>
                
                <!-- نسخة من الزر الأصلي -->
                <div class="dropdown">
                    <button class="btn btn-info btn-md py-2 dropdown-toggle w-100" 
                            type="button" 
                            id="selectParticipantsBtn" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false">
                        <i class="fas fa-users-cog"></i>
                        <span class="ms-2">اختيار المتسابقين</span>
                    </button>
                    <ul class="dropdown-menu w-100" aria-labelledby="selectParticipantsBtn">
                        <li><a class="dropdown-item" href="#" onclick="selectAllParticipants()">
                            <i class="fas fa-check-double text-success"></i> اختيار الكل
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectRandomParticipants()">
                            <i class="fas fa-random text-primary"></i> اختيار عشوائي
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectByNumber()">
                            <i class="fas fa-hashtag text-info"></i> اختيار بالعدد
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="selectManually()">
                            <i class="fas fa-hand-pointer text-warning"></i> اختيار يدوي
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="clearSelection()">
                            <i class="fas fa-times"></i> إلغاء الاختيار
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>اختبارات مباشرة:</h3>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="testSystemStatus()">
                        <i class="fas fa-check-circle"></i> فحص حالة النظام
                    </button>
                    <button class="btn btn-success" onclick="testSelectAllParticipants()">
                        <i class="fas fa-users"></i> اختبار: اختيار الكل
                    </button>
                    <button class="btn btn-info" onclick="testSelectRandomParticipants()">
                        <i class="fas fa-random"></i> اختبار: اختيار عشوائي
                    </button>
                    <button class="btn btn-warning" onclick="testUpdateDisplay()">
                        <i class="fas fa-sync"></i> اختبار: تحديث العرض
                    </button>
                    <button class="btn btn-secondary" onclick="clearConsole()">
                        <i class="fas fa-trash"></i> مسح Console
                    </button>
                </div>
            </div>
        </div>

        <!-- معلومات المتغيرات -->
        <div class="row mb-4">
            <div class="col-12">
                <h3>المتغيرات الحالية:</h3>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <tbody>
                            <tr>
                                <td><strong>participants:</strong></td>
                                <td id="participantsVar">فحص...</td>
                            </tr>
                            <tr>
                                <td><strong>selectedParticipants:</strong></td>
                                <td id="selectedParticipantsVar">فحص...</td>
                            </tr>
                            <tr>
                                <td><strong>typeof selectAllParticipants:</strong></td>
                                <td id="selectAllType">فحص...</td>
                            </tr>
                            <tr>
                                <td><strong>typeof bootstrap:</strong></td>
                                <td id="bootstrapType">فحص...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- عرض Console -->
        <div class="console-output" id="console-output">
            <div><strong>Console Output:</strong></div>
            <div>جاهز للاختبار...</div>
        </div>
    </div>

    <!-- نافذة اختيار المتسابقين -->
    <div class="modal fade" id="participantSelectionModal" tabindex="-1" aria-labelledby="participantSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="participantSelectionModalLabel">
                        <i class="fas fa-users-cog"></i> اختبار النافذة التفاعلية
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-center">هذه نافذة اختبار للتأكد من عمل Bootstrap Modal</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // محاكاة المتغيرات
        let participants = ['خالد', 'خليفه', 'عبدالرحمن', 'محمد', 'حمد', 'سامية', 'سارة', 'الدانة', 'الهنوف'];
        let selectedParticipants = [];

        // إعادة توجيه console.log
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');

        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#00ff00';
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // وظائف الاختبار
        function testSystemStatus() {
            console.log('🔍 فحص حالة النظام...');
            
            // فحص Bootstrap
            const bootstrapOk = typeof bootstrap !== 'undefined';
            updateStatus('bootstrapStatus', 'bootstrapText', bootstrapOk, bootstrapOk ? 'متوفر' : 'غير متوفر');
            
            // فحص المشاركين
            const participantsOk = participants && participants.length > 0;
            updateStatus('participantsStatus', 'participantsText', participantsOk, `${participants.length} مشارك`);
            
            // فحص الزر
            const button = document.getElementById('selectParticipantsBtn');
            const buttonOk = button !== null;
            updateStatus('buttonStatus', 'buttonText', buttonOk, buttonOk ? 'موجود' : 'غير موجود');
            
            // فحص الوظائف
            const functionsOk = typeof selectAllParticipants === 'function';
            updateStatus('functionsStatus', 'functionsText', functionsOk, functionsOk ? 'محملة' : 'غير محملة');
            
            // تحديث المتغيرات
            document.getElementById('participantsVar').textContent = JSON.stringify(participants);
            document.getElementById('selectedParticipantsVar').textContent = JSON.stringify(selectedParticipants);
            document.getElementById('selectAllType').textContent = typeof selectAllParticipants;
            document.getElementById('bootstrapType').textContent = typeof bootstrap;
            
            console.log('✅ تم فحص حالة النظام');
        }

        function updateStatus(statusId, textId, isOk, text) {
            const statusEl = document.getElementById(statusId);
            const textEl = document.getElementById(textId);
            
            statusEl.className = `status-indicator ${isOk ? 'status-success' : 'status-error'}`;
            textEl.textContent = text;
        }

        function testSelectAllParticipants() {
            console.log('🧪 اختبار selectAllParticipants...');
            try {
                if (typeof selectAllParticipants === 'function') {
                    selectAllParticipants();
                    console.log('✅ تم تشغيل selectAllParticipants بنجاح');
                } else {
                    console.error('❌ selectAllParticipants ليست وظيفة');
                }
            } catch (error) {
                console.error('❌ خطأ في selectAllParticipants:', error);
            }
        }

        function testSelectRandomParticipants() {
            console.log('🧪 اختبار selectRandomParticipants...');
            try {
                if (typeof selectRandomParticipants === 'function') {
                    selectRandomParticipants();
                    console.log('✅ تم تشغيل selectRandomParticipants بنجاح');
                } else {
                    console.error('❌ selectRandomParticipants ليست وظيفة');
                }
            } catch (error) {
                console.error('❌ خطأ في selectRandomParticipants:', error);
            }
        }

        function testUpdateDisplay() {
            console.log('🧪 اختبار updateSelectionDisplay...');
            try {
                if (typeof updateSelectionDisplay === 'function') {
                    updateSelectionDisplay();
                    console.log('✅ تم تشغيل updateSelectionDisplay بنجاح');
                } else {
                    console.error('❌ updateSelectionDisplay ليست وظيفة');
                }
            } catch (error) {
                console.error('❌ خطأ في updateSelectionDisplay:', error);
            }
        }

        function clearConsole() {
            consoleOutput.innerHTML = '<div><strong>Console Output:</strong></div><div>تم مسح Console...</div>';
        }

        // تشغيل فحص أولي
        window.addEventListener('load', function() {
            setTimeout(testSystemStatus, 1000);
        });
    </script>
    
    <!-- تحميل الوظائف من الملف الأصلي -->
    <script src="static/script.js"></script>
</body>
</html>
