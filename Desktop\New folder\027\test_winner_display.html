<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عرض الفائز</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            margin: 10px;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 10px;
        }
        #result-section {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            border: 2px dashed #ddd;
        }
        #winner-name {
            font-size: 2rem;
            font-weight: bold;
            margin: 15px 0;
        }
        #winner-details {
            font-size: 1.1rem;
            margin: 10px 0;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug"></i> اختبار عرض الفائز
        </h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>اختبارات القرعة العادية:</h3>
                <button class="btn btn-success test-button" onclick="testNormalWinner()">
                    <i class="fas fa-trophy"></i> اختبار فائز عادي
                </button>
                <button class="btn btn-warning test-button" onclick="testNormalWinnerWithEmail()">
                    <i class="fas fa-envelope"></i> فائز مع إيميل
                </button>
                <button class="btn btn-info test-button" onclick="testNormalWinnerComplete()">
                    <i class="fas fa-user-check"></i> فائز مع بيانات كاملة
                </button>
            </div>
            
            <div class="col-md-6">
                <h3>اختبارات القرعة العاجلة:</h3>
                <button class="btn btn-danger test-button" onclick="testUrgentWinner()">
                    <i class="fas fa-bolt"></i> اختبار فائز عاجل
                </button>
                <button class="btn btn-dark test-button" onclick="testUrgentWinnerComplete()">
                    <i class="fas fa-fire"></i> فائز عاجل كامل
                </button>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h3>اختبارات العجلة الدوارة:</h3>
                <button class="btn btn-primary test-button" onclick="testWheelWinner()">
                    <i class="fas fa-dharmachakra"></i> اختبار فائز العجلة
                </button>
                <button class="btn btn-secondary test-button" onclick="testResetLottery()">
                    <i class="fas fa-redo"></i> اختبار إعادة التعيين
                </button>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h3>اختبارات الأخطاء:</h3>
                <button class="btn btn-outline-danger test-button" onclick="testInvalidData()">
                    <i class="fas fa-exclamation-triangle"></i> بيانات خاطئة
                </button>
                <button class="btn btn-outline-warning test-button" onclick="testMissingElements()">
                    <i class="fas fa-question-circle"></i> عناصر مفقودة
                </button>
            </div>
        </div>

        <!-- منطقة عرض النتائج -->
        <div id="result-section" style="display: none;">
            <div class="alert alert-success text-center">
                <h6><i class="fas fa-trophy"></i> الفائز هو:</h6>
                <h4 id="winner-name" class="text-primary mb-2"></h4>
                <p id="winner-details" class="mb-3"></p>
                <button class="btn btn-secondary btn-sm" onclick="resetLottery()">
                    <i class="fas fa-redo"></i> قرعة جديدة
                </button>
            </div>
        </div>

        <!-- منطقة أزرار القرعة -->
        <div id="lottery-section">
            <div class="text-center mt-3">
                <p class="text-muted">أزرار القرعة (مخفية عند عرض النتيجة)</p>
            </div>
        </div>

        <!-- منطقة التحميل -->
        <div id="loading" style="display: none;" class="text-center">
            <div class="spinner-border text-warning mb-2" role="status">
                <span class="visually-hidden">جاري السحب...</span>
            </div>
            <p class="text-muted small">جاري سحب القرعة...</p>
        </div>

        <!-- عرض رسائل Console -->
        <div class="console-output" id="console-output">
            <div><strong>Console Output:</strong></div>
            <div>جاهز للاختبار...</div>
        </div>
    </div>

    <script>
        // إعادة توجيه console.log لعرضه في الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');

        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#00ff00';
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // بيانات اختبار
        const testData = {
            normal: {
                winner: {
                    id: 1,
                    name: "أحمد محمد",
                    email: "<EMAIL>",
                    phone: "0123456789"
                },
                total_participants: 10,
                type: 'normal'
            },
            normalNoEmail: {
                winner: {
                    id: 2,
                    name: "فاطمة علي",
                    email: "",
                    phone: "0987654321"
                },
                total_participants: 8,
                type: 'normal'
            },
            normalComplete: {
                winner: {
                    id: 3,
                    name: "محمد حسن",
                    email: "<EMAIL>",
                    phone: "0555123456"
                },
                total_participants: 15,
                type: 'normal'
            },
            urgent: {
                winner: {
                    id: 4,
                    name: "عائشة أحمد",
                    email: null,
                    phone: null
                },
                total_participants: 5,
                type: 'urgent'
            },
            urgentComplete: {
                winner: {
                    id: 5,
                    name: "علي محمود",
                    email: "<EMAIL>",
                    phone: "0777888999"
                },
                total_participants: 12,
                type: 'urgent'
            }
        };

        // وظائف الاختبار
        function testNormalWinner() {
            console.log('🧪 اختبار فائز عادي');
            displayWinner(testData.normal);
            document.getElementById('result-section').style.display = 'block';
        }

        function testNormalWinnerWithEmail() {
            console.log('🧪 اختبار فائز عادي مع إيميل');
            displayWinner(testData.normalNoEmail);
            document.getElementById('result-section').style.display = 'block';
        }

        function testNormalWinnerComplete() {
            console.log('🧪 اختبار فائز عادي مع بيانات كاملة');
            displayWinner(testData.normalComplete);
            document.getElementById('result-section').style.display = 'block';
        }

        function testUrgentWinner() {
            console.log('🧪 اختبار فائز عاجل');
            displayWinner(testData.urgent, true);
            document.getElementById('result-section').style.display = 'block';
        }

        function testUrgentWinnerComplete() {
            console.log('🧪 اختبار فائز عاجل مع بيانات كاملة');
            displayWinner(testData.urgentComplete, true);
            document.getElementById('result-section').style.display = 'block';
        }

        function testWheelWinner() {
            console.log('🧪 اختبار فائز العجلة');
            // محاكاة متغير participants للعجلة
            window.participants = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي'];
            showWheelWinner('محمد حسن');
        }

        function testResetLottery() {
            console.log('🧪 اختبار إعادة التعيين');
            resetLottery();
        }

        function testInvalidData() {
            console.log('🧪 اختبار بيانات خاطئة');
            displayWinner(null);
            displayWinner({});
            displayWinner({winner: null});
            displayWinner({winner: {name: ""}});
        }

        function testMissingElements() {
            console.log('🧪 اختبار عناصر مفقودة');
            // إخفاء العناصر مؤقتاً
            const winnerName = document.getElementById('winner-name');
            const originalDisplay = winnerName.style.display;
            winnerName.style.display = 'none';
            
            displayWinner(testData.normal);
            
            // إعادة إظهار العنصر
            winnerName.style.display = originalDisplay;
        }

        // تضمين الوظائف من script.js
    </script>
    <script src="static/script.js"></script>
</body>
</html>
