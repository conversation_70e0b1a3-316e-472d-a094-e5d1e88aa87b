🎯 دليل زر اختيار المتسابقين
=============================

✅ تم إضافة زر اختيار المتسابقين بنجاح!

🎯 الميزة الجديدة:
-----------------

🔘 **زر اختيار المتسابقين**:
- موضع: أعلى أزرار القرعة
- لون: أزرق (info)
- أيقونة: ⚙️ users-cog
- نص: "اختيار المتسابقين"

📋 خيارات الاختيار:
-------------------

### 🔄 **اختيار الكل**:
- يختار جميع المشاركين
- مفيد للعودة للوضع الافتراضي
- أيقونة: ✅ check-double

### 🎲 **اختيار عشوائي**:
- يختار 5 مشاركين عشوائياً
- سريع ومناسب للاختبار
- أيقونة: 🔀 random

### 🔢 **اختيار بالعدد**:
- يطلب عدد المشاركين المطلوب
- يختار العدد المحدد عشوائياً
- أيقونة: # hashtag

### 👆 **اختيار يدوي**:
- يفتح نافذة تفاعلية
- اختيار دقيق للمشاركين
- أيقونة: 👆 hand-pointer

### ❌ **إلغاء الاختيار**:
- يلغي جميع الاختيارات
- يعود لاستخدام جميع المشاركين
- أيقونة: ❌ times

🖥️ النافذة التفاعلية:
--------------------

### 📋 **قسم جميع المشاركين**:
- عرض جميع المشاركين المتاحين
- أزرار + و - لإضافة/إزالة
- تمييز المشاركين المختارين

### ✅ **قسم المختارين**:
- عرض المشاركين المختارين فقط
- إمكانية إزالة من القائمة
- عداد المختارين

### 🎲 **اختيار عشوائي سريع**:
- حقل إدخال العدد المطلوب
- زر اختيار فوري
- الحد الأقصى: عدد المشاركين الكلي

### ⚡ **إجراءات سريعة**:
- زر "اختيار الكل"
- زر "إلغاء الكل"
- تحديث فوري للقوائم

🎨 تغييرات الواجهة:
-------------------

### 🔘 **حالات الزر**:

#### **لا يوجد اختيار** (افتراضي):
```
🔧 اختيار المتسابقين
لون: أزرق (info)
```

#### **اختيار جزئي**:
```
👥 5 مختارين
لون: أزرق أساسي (primary)
```

#### **اختيار الكل**:
```
✅ جميع المشاركين
لون: أخضر (success)
```

### 📱 **رسائل التوست**:
- رسائل تأكيد للإجراءات
- ألوان مختلفة حسب النوع
- اختفاء تلقائي بعد 3 ثوان

🔧 الوظائف المضافة:
-------------------

### 📄 **JavaScript**:
```javascript
// متغيرات جديدة
let selectedParticipants = []; // المتسابقين المختارين

// وظائف رئيسية
selectAllParticipants()      // اختيار الكل
selectRandomParticipants()   // اختيار عشوائي
selectByNumber()             // اختيار بالعدد
selectManually()             // اختيار يدوي
clearSelection()             // إلغاء الاختيار

// وظائف مساعدة
updateSelectionDisplay()     // تحديث عرض الزر
updateWheelWithSelection()   // تحديث العجلة
populateParticipantModal()   // ملء النافذة
toggleParticipant()          // تبديل مشارك
showToast()                  // عرض رسائل
```

### 📄 **Python (app.py)**:
```python
# دعم POST للقرعة
@app.route('/draw_lottery', methods=['GET', 'POST'])

# استلام المشاركين المختارين
selected_participants = data['selected_participants']

# تصفية المشاركين
participants = [p for p in all_participants 
               if p[1] in selected_participants]

# حفظ مع تمييز
result_text = f"🎯 {winner[1]} (من {len(participants)} مختارين)"
```

### 📄 **HTML**:
```html
<!-- زر القائمة المنسدلة -->
<div class="dropdown">
    <button class="btn btn-info dropdown-toggle">
        اختيار المتسابقين
    </button>
    <ul class="dropdown-menu">
        <!-- خيارات الاختيار -->
    </ul>
</div>

<!-- النافذة التفاعلية -->
<div class="modal" id="participantSelectionModal">
    <!-- محتوى النافذة -->
</div>
```

🎯 كيفية الاستخدام:
-------------------

### 1️⃣ **الاختيار السريع**:
```
1. اضغط على "اختيار المتسابقين"
2. اختر "اختيار عشوائي" أو "اختيار بالعدد"
3. سيتم تحديث الزر ليظهر العدد المختار
4. اسحب القرعة كالمعتاد
```

### 2️⃣ **الاختيار اليدوي**:
```
1. اضغط على "اختيار المتسابقين"
2. اختر "اختيار يدوي"
3. في النافذة:
   - اضغط + لإضافة مشارك
   - اضغط - لإزالة مشارك
   - أو استخدم الإجراءات السريعة
4. اضغط "تطبيق الاختيار"
5. اسحب القرعة
```

### 3️⃣ **إلغاء الاختيار**:
```
1. اضغط على "اختيار المتسابقين"
2. اختر "إلغاء الاختيار"
3. سيعود البرنامج لاستخدام جميع المشاركين
```

📊 تأثير على القرعات:
---------------------

### 🎯 **القرعة العادية**:
- تستخدم المشاركين المختارين فقط
- تظهر "من X مختارين" في النتيجة
- تحفظ في التاريخ مع التمييز

### ⚡ **القرعة العاجلة**:
- نفس التأثير
- تعمل مع المختارين فقط

### 🎡 **العجلة الدوارة**:
- تظهر المختارين فقط في العجلة
- تدور بين المختارين
- النتيجة من المختارين

📱 التجاوب والتوافق:
--------------------

### 🖥️ **الشاشات الكبيرة**:
- زر كامل العرض
- نافذة كبيرة (modal-lg)
- عرض مريح للقوائم

### 📱 **الأجهزة المحمولة**:
- زر متجاوب
- نافذة تتكيف مع الشاشة
- سهولة في اللمس

### 🎨 **الألوان والتصميم**:
- متناسق مع التصميم العام
- ألوان Bootstrap
- أيقونات Font Awesome

🔍 مثال عملي:
--------------

### 📋 **السيناريو**:
```
لديك 20 مشارك، تريد قرعة بين 5 منهم فقط
```

### 🎯 **الخطوات**:
```
1. اضغط "اختيار المتسابقين"
2. اختر "اختيار بالعدد"
3. أدخل "5" في النافذة
4. اضغط "اختيار"
5. سيظهر الزر: "👥 5 مختارين"
6. اسحب القرعة العادية أو العاجلة
7. النتيجة ستكون من الـ5 المختارين فقط
```

🎉 الفوائد المحققة:
------------------

### ✅ **مرونة أكبر**:
- اختيار مجموعات محددة
- قرعات متخصصة
- تحكم دقيق

### ✅ **سهولة الاستخدام**:
- واجهة بديهية
- خيارات متنوعة
- رسائل واضحة

### ✅ **توفير الوقت**:
- اختيار سريع
- عدم الحاجة لحذف مشاركين
- إعادة استخدام سهلة

### ✅ **تتبع أفضل**:
- حفظ مع تمييز
- تاريخ واضح
- معرفة المصدر

🎯 الميزة جاهزة للاستخدام!
============================

الآن يمكنك اختيار مجموعة محددة من المتسابقين
وإجراء القرعة عليهم فقط بسهولة ومرونة كاملة!
