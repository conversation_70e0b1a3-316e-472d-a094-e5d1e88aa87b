🎯 برنامج القرعة الإلكترونية - النسخة المحمولة
Electronic Lottery System - Portable Version
=============================================

📦 معلومات الحزمة:
------------------
📄 اسم الملف: Electronic_Lottery_Portable_20250529_195034.zip
📏 الحجم: 0.04 MB
📅 تاريخ الإنشاء: 2025-05-29 19:50:34
🔢 الإصدار: 2.0 Portable

🚀 طريقة التثبيت:
-----------------
1. استخرج ملف ZIP إلى أي مجلد
2. Windows: انقر مرتين على START_LOTTERY.bat
3. Linux/Mac: ./start_lottery.sh

📋 المتطلبات:
-------------
✅ Python 3.7 أو أحدث
✅ اتصال بالإنترنت (للتثبيت الأولي فقط)
✅ متصفح ويب حديث
✅ 50 MB مساحة فارغة

🎨 الميزات:
-----------
✅ واجهة عربية متجاوبة
✅ عجلة دوارة تفاعلية كبيرة (550×550px)
✅ قرعة عادية مع تأثيرات كاملة
✅ قرعة عاجلة سريعة
✅ تأثيرات بصرية وصوتية محسنة
✅ حفظ تاريخ القرعات
✅ دعم الأجهزة المحمولة
✅ إضافة مشاركين فردي أو جماعي
✅ مؤشر دقيق للعجلة مع تمييز الفائز
✅ أصوات متقدمة للدوران والفوز

🔧 الملفات المضمنة:
-------------------
📄 app.py - الملف الرئيسي
📄 requirements.txt - متطلبات Python
📂 static/ - ملفات CSS و JavaScript
📂 templates/ - قوالب HTML
🚀 START_LOTTERY.bat - تشغيل سريع (Windows)
🚀 start_lottery.sh - تشغيل سريع (Linux/Mac)
🔧 setup.py - إعداد أولي
📋 دليل_الاستخدام.md - دليل مفصل
📋 تعليمات_التثبيت.txt - تعليمات سريعة
⚙️ config.ini - ملف الإعدادات

🌐 الوصول للبرنامج:
-------------------
🖥️ محلي: http://localhost:5010
🌍 شبكة: http://[عنوان-IP]:5010

📞 الدعم:
---------
📄 راجع ملف "دليل_الاستخدام.md"
📄 راجع ملف "تعليمات_التثبيت.txt"
📄 راجع ملف "lottery_log.txt" عند حدوث أخطاء

🎉 استمتع بالبرنامج!
===================
